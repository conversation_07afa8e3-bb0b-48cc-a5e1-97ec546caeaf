"""Contains information and properties pertaining to Rez package."""

name = "cgame_avatar_factory"
authors = ["Insectyliu"]
uuid = "4bf3584d-983d-43b6-b84c-2a164fe1c11d"
description = "avatar tools in Maya for cgame."
homepage = "https://git.woa.com/lightbox/internal/cgame_avatar_factory"
tools = []
build_requires = []
private_build_requires = [
    "rez_builder-0",
    "setuptools_scm-1.15",
]


requires = [
    "python-3.7..3.11",
    "maya-2022..2024",
    "lightbox_config-1",
    "lightbox_paths-0",
    "lightbox_log-0",
    "dayu_widgets-0",
    "qtpy-1.11",
    "setuptools-41..71",
    "blinker-1.4",
    "lightbox_ui-0",
    "dcc_menu_api-0",
    "metahuman_dnaclib-1.2",
    "maya_metahuman_dna_viewer-1.2",
    "maya_dna_viewer_resource-1",
    "scipy-1",
    "blade_client_reporter-0",
    "photoshop_python_api-0.13",
    "pillow-9",
    "maya_gs_coverter-0",  
]
# Internal variable used by `rezolve_me` for dev environment.
# Overrides `requires`. Uncomment this only if needed.
dev_requires = requires + ["pytest-4.6", "pytest_cov-2.10", "pyside2"]
variants = [
    ["maya-2022", "pymel-1.2"],
    ["maya-2023", "pymel-1.4"],
]

TEST_COMMAND = (
    "pytest-maya --cov=cgame_avatar_factory --pyargs cgame_avatar_factory/test"
    " --ignore=cgame_avatar_factory/hair_studio"
)
TEST_COMMAND_HAIR = "pytest -v -x --pyargs cgame_avatar_factory/hair_studio/tests"

tests = {
    "maya-2022": {
        "command": TEST_COMMAND,
        "requires": [
            "pytest_maya-0",
            "pytest-4.6",
            "pytest_cov-2.10",
            "maya-2022",
            "python-3.7",
        ],
    },
    "maya-2023": {
        "command": TEST_COMMAND,
        "requires": [
            "pytest_maya-0",
            "pytest-4.6",
            "pytest_cov-2.10",
            "maya-2023",
            "python-3.9",
        ],
    },
    "hair_py37": {
        "command": TEST_COMMAND_HAIR,
        "requires": [
            "pytest_qt-4",
            "pytest-4.6",
            "pytest_cov-2.10",
            "python-3.7",
        ],
    },
    "hair_py39": {
        "command": TEST_COMMAND_HAIR,
        "requires": [
            "pytest_qt-4",
            "pytest-4.6",
            "pytest_cov-2.10",
            "python-3.9",
        ],
    },
}
thm_actions = {
    "lm": {
        "command": "maya",
        "requires": ["maya-2022", "python-3.7"],
        "env": {
            "PYTHONPATH": {
                "action": "prepend",
                "value": "{this.root};",
            },
            "CGAME_AVATAR_FACTORY_FACE_RESOURCE": {
                "action": "prepend",
                "value": "{this.root}/cgame_avatar_factory/face_sculpting_center/resources",
            },
            "CGAME_AVATAR_FACTORY_BODY_RESOURCE": {
                "action": "prepend",
                "value": "{this.root}/cgame_avatar_factory/body_workshop/resources",
            },
            "CGAME_AVATAR_FACTORY_ANIM_RESOURCE": {
                "action": "prepend",
                "value": "{this.root}/cgame_avatar_factory/animation_theater/resources",
            },
            "CGAME_AVATAR_FACTORY_COMMON_RESOURCE": {
                "action": "prepend",
                "value": "{this.root}/cgame_avatar_factory/common/resources",
            },
            "THM_LOG_LEVEL": {
                "action": "set",
                "value": "DEBUG",
            },
            "THM_MENU_CONFIG_PATH": {
                "action": "prepend",
                "value": "{this.root}/config/menu.yaml",
            },
        },
        "tox_options": {
            "passenv": "*",
            "description": "my custom action",
        },
    },
    "lm_23": {
        "command": "maya",
        "requires": ["maya-2023", "python-3.9", "pymel-1.4"],
        "env": {
            "PYTHONPATH": {
                "action": "prepend",
                "value": "{this.root};",
            },
            "CGAME_AVATAR_FACTORY_FACE_RESOURCE": {
                "action": "prepend",
                "value": "{this.root}/cgame_avatar_factory/face_sculpting_center/resources",
            },
            "CGAME_AVATAR_FACTORY_BODY_RESOURCE": {
                "action": "prepend",
                "value": "{this.root}/cgame_avatar_factory/body_workshop/resources",
            },
            "CGAME_AVATAR_FACTORY_ANIM_RESOURCE": {
                "action": "prepend",
                "value": "{this.root}/cgame_avatar_factory/animation_theater/resources",
            },
            "CGAME_AVATAR_FACTORY_COMMON_RESOURCE": {
                "action": "prepend",
                "value": "{this.root}/cgame_avatar_factory/common/resources",
            },
            "THM_LOG_LEVEL": {
                "action": "set",
                "value": "DEBUG",
            },
        },
        "tox_options": {
            "passenv": "*",
            "description": "my custom action",
        },
    },
    "hair_dev": {
        "command": "mayapy",
        "requires": ["maya-2022", "python-3.7", "pydantic-2.5"],
        "env": {
            "PYTHONPATH": {
                "action": "prepend",
                "value": "{this.root};",
            },
            "CGAME_AVATAR_FACTORY_COMMON_RESOURCE": {
                "action": "prepend",
                "value": "{this.root}/cgame_avatar_factory/common/resources",
            },
            "THM_LOG_LEVEL": {
                "action": "set",
                "value": "DEBUG",
            },
            "THM_MENU_CONFIG_PATH": {
                "action": "prepend",
                "value": "{this.root}/config/menu.yaml",
            },
        },
        "tox_options": {
            "passenv": "*",
            "description": "Hair Studio development environment using mayapy",
        },
    },
}


def commands():
    """Set up package."""
    env.PYTHONPATH.prepend("{this.root}/site-packages")  # noqa: F821
    env.THM_MENU_CONFIG_PATH.prepend("{this.root}/config/menu.yaml")  # noqa: F821
    env.CGAME_AVATAR_FACTORY_SRC_PATH.prepend(
        "{this.root}/site-packages/{this.name}",
    )  # noqa: F821
    env.CGAME_AVATAR_FACTORY_FACE_RESOURCE.prepend(
        "{this.root}/site-packages/cgame_avatar_factory/face_sculpting_center/resources",
    )  # noqa: F821
    env.CGAME_AVATAR_FACTORY_BODY_RESOURCE.prepend(
        "{this.root}/site-packages/cgame_avatar_factory/body_workshop/resources",
    )  # noqa: F821
    env.CGAME_AVATAR_FACTORY_COMMON_RESOURCE.prepend(
        "{this.root}/site-packages/cgame_avatar_factory/common/resources",
    )  # noqa: F821
    env.CGAME_AVATAR_FACTORY_ANIM_RESOURCE.prepend(
        "{this.root}/site-packages/cgame_avatar_factory/animation_theater/resources",
    )  # noqa: F821
