#!/usr/bin/env python
"""测试合并按钮功能的脚本

这个脚本用于测试整体变形器中合并后的按钮功能是否正常工作。
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def test_left_panel_merged_buttons():
    """测试左侧面板合并后的按钮功能"""
    print("=== 测试左侧面板合并按钮功能 ===")
    
    try:
        from cgame_avatar_factory.hair_studio.ui.card_hair_modify.components.left_panel_widget import LeftPanelWidget
        
        # 检查类定义
        print("✓ LeftPanelWidget 导入成功")
        
        # 检查信号定义
        expected_signals = [
            'curve_generated',  # 现在由"生成曲线"按钮触发
            'hair_object_selected',  # 现在由"生成曲线"按钮触发
            'mesh_object_selected',  # 现在由"创建驱动>>"按钮触发
            'driver_create_requested'  # 保留但不再使用
        ]
        
        for signal_name in expected_signals:
            if hasattr(LeftPanelWidget, signal_name):
                print(f"✓ 信号存在: {signal_name}")
            else:
                print(f"✗ 信号缺失: {signal_name}")
                return False
        
        # 检查方法定义
        expected_methods = [
            'on_generate_curve',  # 合并后的生成曲线方法
            'on_create_driver',   # 合并后的创建驱动方法
            'clear_all_data'      # 清除数据方法
        ]
        
        for method_name in expected_methods:
            if hasattr(LeftPanelWidget, method_name):
                print(f"✓ 方法存在: {method_name}")
            else:
                print(f"✗ 方法缺失: {method_name}")
                return False
        
        print("=== 左侧面板合并按钮功能测试完成 ===\n")
        return True
        
    except Exception as e:
        print(f"✗ 左侧面板测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_right_panel_merged_buttons():
    """测试右侧面板合并后的按钮功能"""
    print("=== 测试右侧面板合并按钮功能 ===")
    
    try:
        from cgame_avatar_factory.hair_studio.ui.card_hair_modify.components.right_panel_widget import RightPanelWidget
        
        # 检查类定义
        print("✓ RightPanelWidget 导入成功")
        
        # 检查信号定义
        expected_signals = [
            'curvature_changed',
            'influence_range_changed',
            'selecte_affected_faces',  # 现在由"设置影响面片"按钮触发
            'on_set_affect_faces'      # 保留但不再使用
        ]
        
        for signal_name in expected_signals:
            if hasattr(RightPanelWidget, signal_name):
                print(f"✓ 信号存在: {signal_name}")
            else:
                print(f"✗ 信号缺失: {signal_name}")
                return False
        
        # 检查方法定义
        expected_methods = [
            'on_set_affect_faces',  # 合并后的设置影响面片方法
            'update_affected_faces_display',
            'get_affected_objects_from_ui',
            'clear_affected_objects_display'
        ]
        
        for method_name in expected_methods:
            if hasattr(RightPanelWidget, method_name):
                print(f"✓ 方法存在: {method_name}")
            else:
                print(f"✗ 方法缺失: {method_name}")
                return False
        
        print("=== 右侧面板合并按钮功能测试完成 ===\n")
        return True
        
    except Exception as e:
        print(f"✗ 右侧面板测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_ui_event_manager_merged_methods():
    """测试UI事件管理器的合并方法"""
    print("=== 测试UI事件管理器合并方法 ===")
    
    try:
        from cgame_avatar_factory.hair_studio.manager.global_deformer_ui_event_manager import GlobalDeformerUIEventManager
        from cgame_avatar_factory.hair_studio.data.gwrap_data import GlobalDeformerManager
        
        # 创建测试实例
        data_manager = GlobalDeformerManager()
        ui_event_manager = GlobalDeformerUIEventManager(data_manager)
        
        print("✓ GlobalDeformerUIEventManager 创建成功")
        
        # 检查合并后的方法
        expected_methods = [
            'handle_generate_curve_event',      # 合并：选择毛发面片 + 生成曲线
            'handle_create_driver_event',       # 合并：选择mesh对象 + 创建驱动
            'handle_set_affected_objects_event', # 合并：选择受影响面片 + 设置影响面片
            '_get_current_curve_data'           # 辅助方法：获取当前曲线数据
        ]
        
        for method_name in expected_methods:
            if hasattr(ui_event_manager, method_name):
                print(f"✓ 方法存在: {method_name}")
            else:
                print(f"✗ 方法缺失: {method_name}")
                return False
        
        print("=== UI事件管理器合并方法测试完成 ===\n")
        return True
        
    except Exception as e:
        print(f"✗ UI事件管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始测试合并按钮功能...\n")
    
    tests = [
        test_left_panel_merged_buttons,
        test_right_panel_merged_buttons,
        test_ui_event_manager_merged_methods
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
        else:
            print(f"测试失败: {test_func.__name__}\n")
    
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！合并按钮功能实现正确。")
        return True
    else:
        print("❌ 部分测试失败，请检查实现。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
