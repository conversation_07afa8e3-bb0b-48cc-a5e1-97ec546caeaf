"""Base Hair Tab Module.

This module provides the base class for all hair type tabs in the Hair Studio tool.
"""

# Import built-in modules
# Import standard library
import logging
import os

# Import third-party modules
# Import Qt modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList
from cgame_avatar_factory.hair_studio.ui.editor_area import EditorArea
from cgame_avatar_factory.hair_studio.ui.card_hair_modify.global_deformer_widget import GlobalDeformerWidget
from cgame_avatar_factory.hair_studio.ui.card_hair_modify.gs_converter_widget import GSConverterWidget

# AssetLibrary implementation selection
# Use environment variable or default to optimized version
USE_LISTVIEW_ASSET_LIBRARY = os.environ.get("HAIR_STUDIO_USE_LISTVIEW_LIBRARY", "false").lower() == "true"

# Import local modules
from cgame_avatar_factory.hair_studio.constants import LAYOUT_MARGIN_ZERO
from cgame_avatar_factory.hair_studio.constants import LAYOUT_SPACING_SMALL

# Backup implementations available:
# from cgame_avatar_factory.hair_studio.ui.asset_library.asset_library import AssetLibrary  # Original version
from cgame_avatar_factory.hair_studio.constants import ERROR_MSG_ERROR_DELETING_COMPONENT
from cgame_avatar_factory.hair_studio.constants import ERROR_MSG_ERROR_REFRESHING_ASSET_LIBRARY
from cgame_avatar_factory.hair_studio.constants import ERROR_MSG_ERROR_SELECTING_COMPONENT
from cgame_avatar_factory.hair_studio.constants import ERROR_MSG_ERROR_SETTING_SELECTED_COMPONENT
from cgame_avatar_factory.hair_studio.constants import ERROR_MSG_ERROR_UPDATING_COMPONENT_LIST
from cgame_avatar_factory.hair_studio.constants import ERROR_MSG_FAILED_TO_CONNECT_SIGNALS
from cgame_avatar_factory.hair_studio.constants import MINIMUM_WIDTH_ASSET_LIBRARY
from cgame_avatar_factory.hair_studio.constants import MINIMUM_WIDTH_COMPONENT_LIST
from cgame_avatar_factory.hair_studio.constants import MINIMUM_WIDTH_EDITOR_AREA
from cgame_avatar_factory.hair_studio.constants import STRETCH_FACTOR_ASSET_LIBRARY
from cgame_avatar_factory.hair_studio.constants import STRETCH_FACTOR_COMPONENT_LIST
from cgame_avatar_factory.hair_studio.constants import STRETCH_FACTOR_EDITOR_AREA
from cgame_avatar_factory.hair_studio.ui.asset_library.listview_implementation.responsive_asset_library import (
    ResponsiveAssetLibrary as AssetLibrary,
)

import cgame_avatar_factory.hair_studio.constants as constants


class BaseHairTab(QtWidgets.QWidget):
    """Base class for hair type tabs.

    This class provides common functionality for all hair type tabs (Card, XGen, Curve).
    Each tab contains three main areas: editor area, component list, and asset library.
    """

    def __init__(self, hair_type, hair_manager, parent=None):
        """Initialize the BaseHairTab.

        Args:
            hair_type (str): Type of hair ('card', 'xgen', 'curve')
            hair_manager (HairManager): The hair manager instance
            parent (QWidget, optional): The parent widget. Defaults to None.
        """
        super(BaseHairTab, self).__init__(parent)
        self.hair_type = hair_type
        self._hair_manager = hair_manager
        self._selected_component = None
        self.object_name = "{}HairTab".format(hair_type.capitalize())
        self.setObjectName(self.object_name)

        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        # Flag to prevent signal recursion
        self._updating_selection = False

        # Initialize UI components
        self.setup_ui()

        # Connect signals
        self._connect_signals()

        # Initial refresh (moved after UI setup)
        # Refresh is now handled by the setup_ui method

    def setup_ui(self):
        """Set up the user interface components."""
        try:
            # Main layout
            main_layout = QtWidgets.QHBoxLayout(self)
            main_layout.setContentsMargins(
                LAYOUT_MARGIN_ZERO,
                LAYOUT_MARGIN_ZERO,
                LAYOUT_MARGIN_ZERO,
                LAYOUT_MARGIN_ZERO,
            )
            main_layout.setSpacing(LAYOUT_SPACING_SMALL)

            # Create main areas with the shared hair manager - let each area use its own logger
            self.editor_area = EditorArea(
                self.hair_type,
                self._hair_manager,
                self,
            )
            self.component_list = ComponentList(
                self.hair_type,
                self._hair_manager,
                self,
            )
            self.asset_library = AssetLibrary(
                self.hair_type,
                self._hair_manager,
                self,
            )

            # Create new components for the vertical layout
            self.global_deformer_content = GlobalDeformerWidget(self)
            self.gs_converter_content = GSConverterWidget(self)

            # Create QToolBox widget for the editor area
            self.editor_toolbox = QtWidgets.QToolBox()
            
            self.editor_toolbox.setStyleSheet(constants.EDITOR_TOOLBOX_STYLE)

            # Add pages to the toolbox with custom tab widgets for better icon control
            self._setup_toolbox_tabs()

            # Set the first page as current (毛发编辑区)
            self.editor_toolbox.setCurrentIndex(0)

            # Create horizontal splitter for resizable panels
            self.main_splitter = QtWidgets.QSplitter(QtCore.Qt.Horizontal)
            self.main_splitter.setChildrenCollapsible(
                False,
            )  # Prevent panels from collapsing completely

            # Add the editor toolbox, component list, and asset library to main splitter
            self.main_splitter.addWidget(self.editor_toolbox)
            self.main_splitter.addWidget(self.component_list)
            self.main_splitter.addWidget(self.asset_library)

            # Set initial sizes
            self.main_splitter.setSizes(
                [
                    STRETCH_FACTOR_EDITOR_AREA * 100,
                    STRETCH_FACTOR_COMPONENT_LIST * 100,
                    STRETCH_FACTOR_ASSET_LIBRARY * 100,
                ],
            )

            # Set minimum sizes using constants - 支持用户手动调整
            self.editor_toolbox.setMinimumWidth(MINIMUM_WIDTH_EDITOR_AREA)
            self.component_list.setMinimumWidth(MINIMUM_WIDTH_COMPONENT_LIST)
            self.asset_library.setMinimumWidth(MINIMUM_WIDTH_ASSET_LIBRARY)

            # Add splitter to main layout
            main_layout.addWidget(self.main_splitter)

            # Initial refresh after UI is set up
            self.refresh_asset_library()

        except Exception as e:
            self._logger.error("Failed to set up UI: %s", str(e), exc_info=True)
            QtWidgets.QMessageBox.critical(
                self,
                "Error",
                "Failed to initialize tab UI: {}".format(str(e)),
            )

    def _connect_signals(self):
        """Connect signals between components."""
        try:
            # Asset selection is now only for highlighting, not component creation
            # Component creation happens only through drag-and-drop to component list

            # When a component is selected in the list, update the editor area
            self.component_list.component_selected.connect(self._on_component_selected)

            # When a component is deleted from the list
            self.component_list.component_deleted.connect(self._on_component_deleted)

        except Exception as e:
            self._logger.error(
                "%s: %s",
                ERROR_MSG_FAILED_TO_CONNECT_SIGNALS,
                str(e),
                exc_info=True,
            )

    def _on_component_selected(self, component_data):
        """Handle component selection from the component list.

        Args:
            component_data (dict or None): The selected component data, or None if deselected
        """
        try:
            # Prevent recursion
            if self._updating_selection:
                return

            self._updating_selection = True

            if component_data is None:
                # Clear selection
                self._selected_component = None
                self.editor_area.set_component(None)
                self._logger.debug("Component selection cleared")
                return

            # Extract component ID
            component_id = component_data.get("id") if isinstance(component_data, dict) else None

            if component_id:
                # Update the hair manager's selected component
                self._hair_manager.select_component(component_id)

                # Store the selected component
                self._selected_component = component_data

                # Update the editor area with the component data directly
                self.editor_area.set_component(component_data)

                self._logger.debug(
                    "Component selected: %s (ID: %s)",
                    component_data.get("name", "Unknown"),
                    component_id,
                )
            else:
                self._logger.warning("Component data missing ID: %s", component_data)

        except Exception as e:
            self._logger.error(
                "%s: %s",
                ERROR_MSG_ERROR_SELECTING_COMPONENT,
                str(e),
                exc_info=True,
            )
        finally:
            self._updating_selection = False

    def _on_component_deleted(self, component_id):
        """Handle component deletion from the component list.

        Args:
            component_id (str): ID of the component to delete
        """
        try:
            # Delete the component through the hair manager
            if not self._hair_manager.delete_component(component_id):
                self._logger.warning("Failed to delete component: %s", component_id)

        except Exception as e:
            self._logger.error(
                "%s: %s",
                ERROR_MSG_ERROR_DELETING_COMPONENT,
                str(e),
                exc_info=True,
            )

    def refresh_asset_library(self):
        """Refresh the asset library with the latest data."""
        try:
            # Get assets filtered by the current hair type
            assets = self._hair_manager.get_assets(asset_type=self.hair_type)

            self._logger.debug(
                "BaseHairTab refresh_asset_library: Retrieved %d assets for type '%s'",
                len(assets),
                self.hair_type,
            )

            # Update the asset library
            self.asset_library.update_assets(assets)

        except Exception as e:
            self._logger.error(
                "%s: %s",
                ERROR_MSG_ERROR_REFRESHING_ASSET_LIBRARY,
                str(e),
                exc_info=True,
            )

    def update_component_list(self, components):
        """Update the component list with the given components.

        Args:
            components (list): List of HairComponent objects
        """
        try:
            # Filter components by the current hair type
            filtered_components = [comp for comp in components if comp.get("type") == self.hair_type]

            # Update the component list (this won't emit signals)
            self.component_list.update_components(filtered_components)

            # Explicitly clear selection to avoid signal loops
            self.component_list.clear_selection()

        except Exception as e:
            self._logger.error(
                "%s: %s",
                ERROR_MSG_ERROR_UPDATING_COMPONENT_LIST,
                str(e),
                exc_info=True,
            )

    def set_selected_component(self, component):
        """Set the currently selected component.

        Args:
            component (dict or None): The component data to select, or None to clear selection
        """
        try:
            # Prevent recursion
            if self._updating_selection:
                return

            self._updating_selection = True
            self._selected_component = component

            if component:
                # Update the editor area
                self.editor_area.set_component(component)

                # Get component ID for list selection
                component_id = None
                if isinstance(component, dict):
                    component_id = component.get("id")
                elif hasattr(component, "id"):
                    component_id = component.id

                if component_id:
                    # Select the component in the list
                    self.component_list.select_component(component_id)
                else:
                    self._logger.warning("Component missing ID, cannot select in list")
            else:
                # Clear the editor area by setting component to None
                self.editor_area.set_component(None)

                # Clear the selection in the list
                self.component_list.clear_selection()

        except Exception as e:
            self._logger.error(
                "%s: %s",
                ERROR_MSG_ERROR_SETTING_SELECTED_COMPONENT,
                str(e),
                exc_info=True,
            )
        finally:
            self._updating_selection = False

    def get_hair_type(self):
        """Get the hair type of this tab.

        Returns:
            str: The hair type ('card', 'xgen', or 'curve')
        """
        return self.hair_type

    def get_hair_manager(self):
        """Get the hair manager instance.

        Returns:
            HairManager: The hair manager instance
        """
        return self._hair_manager

    def _setup_toolbox_tabs(self):
        """Setup toolbox tabs with properly aligned icons and text."""
        # Tab configurations: (widget, icon_name, text)
        tab_configs = [
            (self.editor_area, constants.TOOL_BOX_ICON, constants.ATTR_TOOL_TB_LABEL),
            (self.global_deformer_content, constants.TOOL_BOX_ICON, constants.GLOBAL_WRAP_TB_LABEL),
            (self.gs_converter_content, constants.TOOL_BOX_ICON, constants.GS_CONVERT_TB_LABEL),
        ]

        from cgame_avatar_factory.hair_studio.utils.icon_utils import get_icon_with_fallback

        for widget, icon_name, text in tab_configs:
            # Get icon with proper size for tab
            icon = get_icon_with_fallback(icon_name, (16, 16))  # Optimal size for tab icons

            # Add to toolbox with icon and text
            self.editor_toolbox.addItem(widget, icon, f" {text}")  # Add spacing for better alignment
