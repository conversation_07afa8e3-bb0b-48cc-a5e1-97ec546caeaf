"""Hair Studio Manager Module.

This module provides the HairManager class which serves as the central point for
managing hair assets, components, and their interactions in the Hair Studio tool.
"""


# Import local modules
from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
from cgame_avatar_factory.hair_studio.manager.global_deformer_ui_event_manager import GlobalDeformerUIEventManager

__all__ = [
    "HairManager",
    "GlobalDeformerUIEventManager",
]
