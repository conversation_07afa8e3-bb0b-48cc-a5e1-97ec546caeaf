# cgame_avatar_factory\hair_studio\maya_api\__init__.py

"""Maya API Facade.

This module provides a simplified interface to the underlying Maya operations
for the Hair Studio tool. The HairManager should interact with this facade
rather than calling the operations directly.
"""

from . import hair_operations


class MayaAPI(object):
    """
    A facade class that provides a high-level API for Maya operations.
    This class is intended to be used by the HairManager.
    """

    def create_hair_from_asset(self, asset_data):
        """
        High-level function to create a hair representation in Maya from an asset.

        Args:
            asset_data (dict): Dictionary with asset details.

        Returns:
            dict: Result from the low-level operation.
        """
        # Here you could add more complex logic, like choosing the right
        # operation based on asset_type.
        return hair_operations.create_hair_node(asset_data)

    def delete_hair_component(self, component_data):
        """
        High-level function to delete a hair component from Maya.

        Args:
            component_data (dict): Dictionary with component details,
                                   must include 'node_names' (dict).

        Returns:
            dict: Result from the low-level operation.
        """
        node_names = component_data.get("node_names")
        if not node_names:
            return {"success": False, "error": "No node_names in component data."}
        return hair_operations.delete_node(node_names)

    def set_component_visibility(self, component_data, is_visible):
        """
        High-level function to set the visibility of a hair component in Maya.

        Args:
            component_data (dict): Dictionary with component details,
                                   must include 'node_names' (dict).
            is_visible (bool): The desired visibility state.

        Returns:
            dict: Result from the low-level operation.
        """
        node_names = component_data.get("node_names")
        if not node_names:
            return {"success": False, "error": "No node_names in component data."}
        return hair_operations.set_node_visibility(node_names, is_visible)

    def check_component_exists(self, component_data):
        """
        High-level function to check if a hair component exists in Maya.

        Args:
            component_data (dict): Dictionary with component details,
                                   must include 'node_names' (dict).

        Returns:
            dict: Either result from the low-level operation (when node_names exists),
                  or error dict with 'success' and 'error' keys (when node_names missing).
                  Error format: {'success': False, 'error': str} .
        """
        node_names = component_data.get("node_names")
        if not node_names:
            return {"success": False, "error": "No node_names in component data."}
        return hair_operations.check_component_nodes_exist(node_names)

    def select_component(self, component_data):
        """
        High-level function to select a hair component in Maya.

        Args:
            component_data (dict): Dictionary with component details,
                                   must include 'node_names' (dict).

        Returns:
            dict: Result from the low-level operation.
        """
        node_names = component_data.get("node_names")
        if not node_names:
            return {"success": False, "error": "No node_names in component data."}
        return hair_operations.select_component_nodes(node_names)


# Singleton instance for easy access
maya_api_instance = MayaAPI()
