"""Hair Studio Configuration Package.

This package contains configuration modules for the Hair Studio tool.

Usage Scenarios:
1. register_shader_config: Dynamically register new shader configurations at runtime
   - Plugin system: Third-party plugins register custom shaders
   - Project customization: Special shaders needed for specific projects
   - User configuration: User-defined shader parameter combinations

2. create_config_with_overrides: Create variants based on existing configurations
   - Material variants: Different parameter combinations of the same base shader
   - Quality levels: High, medium, low quality shader variants
   - Platform adaptation: Shader parameter adjustments for different platforms

3. get_preset_config: Use predefined shader variants
   - Quick application: Use preset common shader combinations
   - Standardization: Ensure unified shader variants are used in projects
"""

from .shader_configs import ASSET_SHADER_MAPPING
from .shader_configs import PARAMETER_OVERRIDES
from .shader_configs import create_config_with_overrides
from .shader_configs import get_preset_config
from .shader_configs import get_shader_config
from .shader_configs import register_shader_config
from .shader_configs import shader_registry

__all__ = [
    "get_shader_config",
    "register_shader_config",
    "create_config_with_overrides",
    "get_preset_config",
    "shader_registry",
    "ASSET_SHADER_MAPPING",
    "PARAMETER_OVERRIDES",
]
