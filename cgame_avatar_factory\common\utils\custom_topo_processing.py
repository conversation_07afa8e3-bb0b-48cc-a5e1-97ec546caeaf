# Import built-in modules
import json
import os

# Import third-party modules
from PIL import Image
import maya.api.OpenMaya as om
import maya.cmds as cmds
import maya.mel as mel

# Import local modules
import cgame_avatar_factory.common.constants as const
from cgame_avatar_factory.common.utils import utils
import cgame_avatar_factory.face_sculpting_center.constants as face_const
from cgame_avatar_factory.face_sculpting_center.utils import dna_utils as dna
import cgame_avatar_factory.face_sculpting_center.utils.mesh_util as mesh


def _get_texture_callbacks():
    """获取纹理更新回调列表"""
    if not hasattr(_get_texture_callbacks, "_callbacks"):
        _get_texture_callbacks._callbacks = []
    return _get_texture_callbacks._callbacks


def _get_transfermap_folder():
    """获取transfermap文件夹路径"""
    workspace_path = mesh.get_maya_workspace()
    transfermap_folder = os.path.join(workspace_path, "sourceimages", "transfermapbaker")
    os.makedirs(transfermap_folder, exist_ok=True)
    return transfermap_folder


def register_texture_update_callback(callback):
    """注册纹理更新回调"""
    callbacks = _get_texture_callbacks()
    if callback not in callbacks:
        callbacks.append(callback)


def unregister_texture_update_callback(callback):
    """注销纹理更新回调"""
    callbacks = _get_texture_callbacks()
    if callback in callbacks:
        callbacks.remove(callback)


def notify_texture_update():
    """通知所有纹理更新回调"""
    callbacks = _get_texture_callbacks()
    for callback in callbacks:
        callback()


def create_custom_topo_info_node(parent_group):
    node_name = face_const.CUSTOM_TOPO_INFO_NODE_NAME
    if cmds.objExists(node_name):
        cmds.delete(node_name)

    info_node = cmds.createNode("transform", name=node_name)
    if parent_group and cmds.objExists(parent_group):
        cmds.parent(info_node, parent_group)

    attrs = [
        (face_const.CUSTOM_TOPO_INFO_NODE_TEX_ATTR_NAME, "Texture Paths", "[]"),
        (face_const.CUSTOM_TOPO_INFO_NODE_ICON_ATTR_NAME, "Icon Path", ""),
    ]

    for attr_name, nice_name, default_val in attrs:
        cmds.addAttr(info_node, longName=attr_name, niceName=nice_name, dataType="string")
        cmds.setAttr(f"{info_node}.{attr_name}", default_val, type="string")

    return info_node


def _get_info_node_attr(attr_name, default_value=""):
    """通用获取info node属性的函数"""
    info_node = face_const.CUSTOM_TOPO_INFO_NODE_NAME
    if not cmds.objExists(info_node):
        return default_value
    try:
        return cmds.getAttr(f"{info_node}.{attr_name}") or default_value
    except:
        return default_value


def _set_info_node_attr(attr_name, value):
    """通用设置info node属性的函数"""
    info_node = face_const.CUSTOM_TOPO_INFO_NODE_NAME
    if not cmds.objExists(info_node):
        return False
    try:
        cmds.setAttr(f"{info_node}.{attr_name}", value, type="string")
        return True
    except:
        return False


def add_texture_path_to_info_node(texture_path):
    current_paths_str = _get_info_node_attr(face_const.CUSTOM_TOPO_INFO_NODE_TEX_ATTR_NAME, "[]")
    try:
        current_paths = json.loads(current_paths_str)
    except:
        current_paths = []

    if texture_path not in current_paths:
        current_paths.append(texture_path)
        _set_info_node_attr(face_const.CUSTOM_TOPO_INFO_NODE_TEX_ATTR_NAME, json.dumps(current_paths, indent=2))


def get_texture_paths_from_info_node():
    paths_str = _get_info_node_attr(face_const.CUSTOM_TOPO_INFO_NODE_TEX_ATTR_NAME, "[]")
    try:
        return json.loads(paths_str)
    except:
        return []


def set_dna_path_to_info_node(dna_path):
    _set_info_node_attr(face_const.CUSTOM_TOPO_INFO_NODE_ICON_ATTR_NAME, dna_path)


def get_dna_path_from_info_node():
    return _get_info_node_attr(face_const.CUSTOM_TOPO_INFO_NODE_ICON_ATTR_NAME)


def duplication_dx_shader_to_target_mesh(target_mesh_dict):
    """为目标网格复制DX着色器"""
    for mesh_obj in target_mesh_dict.values():
        for target_mesh in mesh_obj:
            _apply_shader_to_mesh(target_mesh)


def _apply_shader_to_mesh(target_mesh):
    """为单个网格应用着色器"""
    for mesh_type, shader_name in const.DX11_MATERIAL_NAME.items():
        if mesh_type in target_mesh:
            new_shader = duplicate_shader_network(shader_name)
            if new_shader:
                _assign_material_to_mesh(target_mesh, new_shader)


def _create_shading_group(material_name, sg_name=None):
    """创建着色器组并连接材质"""
    sg_name = sg_name or f"{material_name}SG"
    if not cmds.objExists(sg_name):
        cmds.sets(renderable=True, noSurfaceShader=True, empty=True, name=sg_name)
        cmds.connectAttr(f"{material_name}.outColor", f"{sg_name}.surfaceShader")
    return sg_name


def _assign_material_to_mesh(mesh, material_name):
    """将材质分配给网格"""
    sg_name = _create_shading_group(material_name)
    cmds.sets(mesh, edit=True, forceElement=sg_name)


def _get_filename_without_ext(file_path):
    """获取不带扩展名的文件名"""
    return os.path.splitext(os.path.basename(file_path))[0]


def _create_transfermap_path(original_path, suffix=""):
    """创建transfermap路径"""
    transfermap_folder = _get_transfermap_folder()
    filename = _get_filename_without_ext(original_path)
    if suffix:
        filename = f"{filename}_{suffix}"
    return os.path.join(transfermap_folder, f"{filename}{const.SUPPORTED_THUMBNAIL_FILE_EXTENSIONS[1]}")


def _get_connected_file_node(node_full_name):
    """获取连接的文件节点"""
    if not cmds.objExists(node_full_name):
        return None
    connected_nodes = cmds.listConnections(node_full_name, source=True, destination=False)
    if connected_nodes and cmds.nodeType(connected_nodes[0]) == "file":
        return connected_nodes[0]
    return None


def _update_shader_texture_paths(new_shader):
    """更新着色器纹理路径"""
    for node_name in face_const.MATRIAL_NODE_NAME.values():
        node_full_name = f"{new_shader}.{node_name}"
        connected_node = _get_connected_file_node(node_full_name)
        if connected_node:
            current_tex_path = cmds.getAttr(f"{connected_node}.fileTextureName")
            new_tex_path = _create_transfermap_path(current_tex_path)
            cmds.setAttr(f"{connected_node}.fileTextureName", new_tex_path, type="string")


def create_blendshape_for_base_mesh():
    """为基础网格创建混合形状"""
    meshes = dna.TemplateDna.get_mesh_name()
    for mesh in meshes:
        wrap_base_mesh = f"{mesh}_wrap_base"
        if cmds.objExists(mesh) and cmds.objExists(wrap_base_mesh):
            blendshape_node = cmds.blendShape(mesh, wrap_base_mesh, name=f"{mesh}_auto_wrap_blendShape", parallel=True)[
                0
            ]
            cmds.setAttr(f"{blendshape_node}.{mesh}", 1)


def wrap_target_mesh(base_mesh_dict, target_mesh_dict):
    """包裹目标网格"""
    for mesh_obj in target_mesh_dict.values():
        for target_mesh in mesh_obj:
            _wrap_single_mesh(target_mesh, base_mesh_dict)


def _wrap_single_mesh(target_mesh, base_meshes):
    """包裹单个网格"""
    matching_base = _find_matching_base_mesh(target_mesh, base_meshes)
    wrap_base = matching_base or f"{const.BASE_HEAD_MESH_NAME}_wrap_base"
    cmds.select(target_mesh, wrap_base)
    mel.eval("CreateWrap")


def _find_matching_base_mesh(target_mesh, base_meshes):
    """查找匹配的基础网格"""
    for mesh_type in face_const.MESH_TYPE:
        if mesh_type in target_mesh:
            for base_mesh in base_meshes:
                if mesh_type in base_mesh:
                    return base_mesh
    return None


def base_mesh_visibility(visibility: bool):
    """设置基础网格可见性"""
    meshes = dna.TemplateDna.get_mesh_name()
    for mesh in meshes:
        if cmds.objExists(mesh):
            cmds.setAttr(f"{mesh}.visibility", visibility)


def build_target_mesh(target_topo_group: str, dna_path: str):
    """构建目标网格"""
    mesh_builder = dna.DNAMeshBuilder(dna_path)
    mesh_objects = mesh_builder._build_meshes()

    lod_grp_list = []
    for lod, mesh_obj in mesh_objects.items():
        lod_grp = cmds.group(empty=True, name=f"lod_{lod}_grp")
        if mesh_obj:
            cmds.parent(mesh_obj, lod_grp)
        lod_grp_list.append(lod_grp)

    create_maya_lod_group(lod_grp_list, target_topo_group)

    return mesh_objects


def build_base_mesh(base_topo_group: str):
    """构建基础网格（默认合并UV以确保UV线连接）"""
    dna_path = os.path.join(const.CONFIG_PATH, const.BASE_DNA_PATH)
    mesh_builder = dna.DNAMeshBuilder(dna_path=dna_path, suffix="wrap_base", merge_uvs=True)
    mesh_objects = mesh_builder._build_meshes()

    if mesh_objects and 0 in mesh_objects:
        cmds.parent(mesh_objects[0], base_topo_group)

    return mesh_objects


def bake_base_mesh_tex(mesh_objects: dict):
    """烘焙基础网格纹理"""
    for source_mesh in mesh_objects:
        _create_material_for_mesh(source_mesh)
    start_baking_process("预览采样", 1024)


def _create_lambert_material(mat_name):
    """创建Lambert材质"""
    if not cmds.objExists(mat_name):
        lambert_mat = cmds.shadingNode("lambert", asShader=True)
        cmds.rename(lambert_mat, mat_name)
    return mat_name


def _create_material_for_mesh(source_mesh):
    """为网格创建材质"""
    mat_name = f"{source_mesh}_mat"
    _create_lambert_material(mat_name)
    _assign_material_to_mesh(source_mesh, mat_name)


def _connect_shader_color(connected_node, mat_name):
    """连接着色器颜色"""
    if cmds.objExists(f"{connected_node}.outColor"):
        cmds.connectAttr(f"{connected_node}.outColor", f"{mat_name}.color", force=True)


def duplicate_shader_network(shader_name: str):
    """复制着色器网络"""
    if not cmds.objExists(shader_name) or cmds.nodeType(shader_name) != "dx11Shader":
        return None

    try:
        new_shader_name = f"{shader_name}_auto_wrap"
        if cmds.objExists(new_shader_name):
            return new_shader_name

        dup = cmds.duplicate(shader_name, upstreamNodes=True)
        renamed_shader = cmds.rename(dup[0], new_shader_name)
        _update_shader_texture_paths(renamed_shader)
        return renamed_shader
    except Exception:
        return None


def merge_alpha_to_color_texture(color_path, alpha_path, output_path):
    """将alpha通道合并到颜色纹理"""
    try:
        with Image.open(color_path) as color_img, Image.open(alpha_path) as alpha_img:
            color_img = color_img.convert("RGB")
            alpha_img = alpha_img.convert("RGBA")
            alpha_channel = alpha_img.split()[-1]
            color_with_alpha = Image.merge("RGBA", (*color_img.split(), alpha_channel))
            color_with_alpha.save(output_path, "PNG")
        return True
    except Exception:
        return False


def check_b_channel_consistency(x, y, pixels, width, height, threshold=100):
    _, _, current_b = pixels[x, y]
    current_b_brightness = current_b / 255.0

    if current_b_brightness < 0.4:
        return False
    neighbors = [(-1, -1), (-1, 0), (-1, 1), (0, -1), (0, 1), (1, -1), (1, 0), (1, 1)]

    for dx, dy in neighbors:
        nx, ny = x + dx, y + dy
        if 0 <= nx < width and 0 <= ny < height:
            _, _, neighbor_b = pixels[nx, ny]
            neighbor_b_brightness = neighbor_b / 255.0

            brightness_diff = abs(current_b_brightness - neighbor_b_brightness) * 255

            if brightness_diff > threshold:
                return False
    return True


def fix_normal_texture(normal_path, output_path=None):
    """修正法线贴图，将不正确的像素设置为默认法线颜色

    Args:
        normal_path: 输入法线贴图路径
        output_path: 输出路径，如果为None则覆盖原文件

    Returns:
        bool: 修正是否成功
    """
    try:
        if output_path is None:
            output_path = normal_path

        default_normal_color = (128, 128, 255)

        with Image.open(normal_path) as normal_image:
            normal_image = normal_image.convert("RGB")
            normal_pixels = normal_image.load()

            width, height = normal_image.size
            for y in range(height):
                for x in range(width):
                    is_consistent = check_b_channel_consistency(
                        x,
                        y,
                        normal_pixels,
                        width,
                        height,
                    )

                    if not is_consistent:
                        normal_pixels[x, y] = default_normal_color
            normal_image.save(output_path, "PNG")
        return True

    except Exception as e:
        return False


def blend_normal_textures(base_normal_path, overlay_normal_path, output_path):
    """叠加两张法线贴图

    Args:
        base_normal_path: 基础法线贴图路径
        overlay_normal_path: 叠加法线贴图路径
        output_path: 输出法线贴图路径

    Returns:
        bool: 叠加是否成功
    """

    with Image.open(base_normal_path) as base_img, Image.open(overlay_normal_path) as overlay_img:
        base_image = base_img.convert("RGB")
        overlay_image = overlay_img.convert("RGB")

        overlay_pixels = overlay_image.load()
        for y in range(overlay_image.size[1]):
            for x in range(overlay_image.size[0]):
                r, g, b = overlay_pixels[x, y]
                g = 255 - g
                overlay_pixels[x, y] = (r, g, b)

        if base_image.size != overlay_image.size:
            overlay_image = overlay_image.resize(base_image.size, Image.LANCZOS)
        result_image = Image.new("RGB", base_image.size)

        base_pixels = base_image.load()
        overlay_pixels = overlay_image.load()
        result_pixels = result_image.load()

        for y in range(base_image.size[1]):
            for x in range(base_image.size[0]):
                base_r, base_g, base_b = base_pixels[x, y]
                overlay_r, overlay_g, overlay_b = overlay_pixels[x, y]

                should_blend = check_b_channel_consistency(
                    x,
                    y,
                    overlay_pixels,
                    base_image.size[0],
                    base_image.size[1],
                )
                if should_blend:
                    base_nx = (base_r / 255.0) * 2.0 - 1.0
                    base_ny = (base_g / 255.0) * 2.0 - 1.0
                    base_nz = (base_b / 255.0) * 2.0 - 1.0

                    overlay_nx = (overlay_r / 255.0) * 2.0 - 1.0
                    overlay_ny = (overlay_g / 255.0) * 2.0 - 1.0
                    overlay_nz = (overlay_b / 255.0) * 2.0 - 1.0

                    result_nx = base_nx + overlay_nx
                    result_ny = base_ny + overlay_ny
                    result_nz = base_nz * overlay_nz

                    length = (result_nx * result_nx + result_ny * result_ny + result_nz * result_nz) ** 0.5
                    if length > 0:
                        result_nx /= length
                        result_ny /= length
                        result_nz /= length

                    result_r = int((result_nx + 1.0) * 0.5 * 255)
                    result_g = int((result_ny + 1.0) * 0.5 * 255)
                    result_b = int((result_nz + 1.0) * 0.5 * 255)

                    result_r = max(0, min(255, result_r))
                    result_g = max(0, min(255, result_g))
                    result_b = max(0, min(255, result_b))

                    result_pixels[x, y] = (result_r, result_g, result_b)
                else:
                    result_pixels[x, y] = (base_r, base_g, base_b)

        result_image.save(output_path, "PNG")
    return True


def surface_sampler_bake(
    target_mesh,
    source_mesh,
    output_path,
    map_width=1024,
    map_height=1024,
    map_output="diffuseRGB",
    uv_set="map1",
    search_offset=0.1,
    file_format="png",
    super_sampling=0,
):
    """表面采样烘焙"""
    try:
        output_path_fixed = output_path.replace("\\", "/")
        mel_command = f"""surfaceSampler -target {target_mesh}Shape -uvSet {uv_set} -searchOffset {search_offset} -maxSearchDistance 0 -searchCage "" -source {source_mesh}Shape -mapOutput {map_output} -mapWidth {map_width} -mapHeight {map_height} -max 0 -mapSpace tangent -mapMaterials 1 -shadows 1 -filename "{output_path_fixed}" -fileFormat "{file_format}" -superSampling {super_sampling} -filterType 0 -filterSize 3 -overscan 1 -searchMethod 0 -useGeometryNormals 1 -ignoreMirroredFaces 0 -flipU 0 -flipV 0"""
        mel.eval(mel_command)
        return True
    except Exception:
        return False


def create_maya_lod_group(lod_grp_list: list, target_topo_group: str):
    """创建Maya LOD组"""
    if not lod_grp_list:
        return

    cmds.select(lod_grp_list, replace=True)
    mel.eval("LevelOfDetailGroup;")

    selected_objects = cmds.ls(selection=True)
    if selected_objects:
        cmds.parent(selected_objects[0], target_topo_group)


def _get_super_sampling_value(bake_type):
    """获取超采样值"""
    return {
        "预览采样": 0,
        "低等采样": 1,
        "中等采样": 2,
        "高等采样": 3,
    }.get(bake_type, 0)


def _process_mesh_baking(mesh, map_size, super_sampling):
    """处理单个网格的烘焙"""
    source_mesh = f"{mesh}_wrap_base"
    target_name = f"{mesh}_{face_const.AUTO_TOPO_SUFFIX}"
    base_materials = f"{source_mesh}_mat"
    transfermap_folder = _get_transfermap_folder()

    if not (cmds.objExists(source_mesh) and cmds.objExists(target_name)):
        return

    alpha_output_path = os.path.join(transfermap_folder, f"{source_mesh}_alpha")
    surface_sampler_bake(
        target_name,
        source_mesh,
        alpha_output_path,
        map_output="alpha",
        map_width=map_size,
        map_height=map_size,
        super_sampling=super_sampling,
    )

    target_materials = _get_mesh_materials(mesh)
    if not target_materials:
        return
    alpha_path_with_ext = f"{alpha_output_path}{const.SUPPORTED_THUMBNAIL_FILE_EXTENSIONS[1]}"
    if "head" in mesh:
        normal_output_path = os.path.join(transfermap_folder, f"{source_mesh}_normal")
        surface_sampler_bake(
            target_name,
            source_mesh,
            normal_output_path,
            map_output="normal",
            map_width=map_size,
            map_height=map_size,
            super_sampling=super_sampling,
        )
        normal_path_with_ext = f"{normal_output_path}{const.SUPPORTED_THUMBNAIL_FILE_EXTENSIONS[1]}"
        fix_normal_texture(normal_path_with_ext)
        merge_alpha_to_color_texture(normal_path_with_ext, alpha_path_with_ext, normal_path_with_ext)
    texture_paths = []
    for node_name in face_const.MATRIAL_NODE_NAME.values():
        node_full_name = f"{target_materials[0]}.{node_name}"
        connected_node = _get_connected_file_node(node_full_name)

        if connected_node:
            current_tex_path = cmds.getAttr(f"{connected_node}.fileTextureName")
            new_tex_path = os.path.join(transfermap_folder, _get_filename_without_ext(current_tex_path))

            _connect_shader_color(connected_node, base_materials)
            surface_sampler_bake(
                target_name,
                source_mesh,
                new_tex_path,
                map_width=map_size,
                map_height=map_size,
                super_sampling=super_sampling,
            )

            new_tex_path_with_ext = f"{new_tex_path}{const.SUPPORTED_THUMBNAIL_FILE_EXTENSIONS[1]}"

            if node_name == "NormalTexture" and "head" in mesh:
                texture_paths.append(normal_path_with_ext)
                blend_normal_textures(new_tex_path_with_ext, normal_path_with_ext, new_tex_path_with_ext)
            else:
                texture_paths.append(new_tex_path_with_ext)
            merge_alpha_to_color_texture(new_tex_path_with_ext, alpha_path_with_ext, new_tex_path_with_ext)
            add_texture_path_to_info_node(new_tex_path_with_ext)

    alpha_file = f"{alpha_output_path}{const.SUPPORTED_THUMBNAIL_FILE_EXTENSIONS[1]}"
    if os.path.exists(alpha_file):
        os.remove(alpha_file)

    return texture_paths


def merge_texture_group(textures: list):
    texture_dict = {}
    for type in face_const.MATRIAL_NODE_NAME.keys():
        texture_list = []
        for texture in textures:
            if type in texture:
                texture_list.append(texture)
        texture_dict[type] = texture_list
    transfermap_folder = _get_transfermap_folder()
    for type, textures in texture_dict.items():
        if len(textures) > 1:
            merge_texture(textures, type, transfermap_folder)


def merge_texture(texture_paths: list, texture_type: str, transfermap_folder: str):
    # Import built-in modules
    import logging

    logger = logging.getLogger(__name__)
    if not texture_paths:
        return

    folder_name = "texture_merge"
    if cmds.objExists(const.NETWORK_NODE_NAME):
        data_str = cmds.getAttr(f"{const.NETWORK_NODE_NAME}.{const.SCENE_STATE_ATTR_NAME}")
        if data_str:
            data = json.loads(data_str)
            dna_file_path = data["ring"]["components"][0]["dna_file_path"]
            folder_name = os.path.splitext(os.path.basename(dna_file_path))[0]
        topo_data_str = cmds.getAttr(
            f"{face_const.CUSTOM_TOPO_INFO_NODE_NAME}.{face_const.CUSTOM_TOPO_INFO_NODE_ICON_ATTR_NAME}"
        )
        if topo_data_str:
            # 从topo路径中提取文件名（不包含扩展名）
            topo_name = os.path.splitext(os.path.basename(topo_data_str))[0]
            folder_name = folder_name + "_" + topo_name

    merge_folder = os.path.join(transfermap_folder, folder_name)
    os.makedirs(merge_folder, exist_ok=True)

    images = []
    for texture_path in texture_paths:
        if os.path.exists(texture_path):
            try:
                with Image.open(texture_path) as img:
                    img_copy = img.copy()
                    images.append(img_copy)
            except Exception as e:
                logger.error(f"Failed to append image {texture_path}: {e}")
                continue

    if not images:
        return

    base_width, base_height = images[0].size

    merged_image = images[0].copy()

    if len(images) == 1:
        output_path = os.path.join(merge_folder, f"{texture_type}.png")
        merged_image.save(output_path, "PNG")
        return

    if merged_image.mode != "RGBA":
        merged_image = merged_image.convert("RGBA")

    for i in range(1, len(images)):
        overlay_img = images[i]

        if overlay_img.size != (base_width, base_height):
            overlay_img = overlay_img.resize((base_width, base_height), Image.LANCZOS)

        if overlay_img.mode != "RGBA":
            overlay_img = overlay_img.convert("RGBA")

        merged_image = Image.alpha_composite(merged_image, overlay_img)

    output_path = os.path.join(merge_folder, f"{texture_type}.png")
    merged_image.save(output_path, "PNG")


def start_baking_process(bake_type, bake_quality):
    """开始烘焙过程"""
    super_sampling = _get_super_sampling_value(bake_type)
    meshes = dna.TemplateDna.get_mesh_name()
    texture_paths = []
    for mesh in meshes:
        paths = _process_mesh_baking(mesh, bake_quality, super_sampling)
        if paths:
            texture_paths.extend(paths)

    merge_texture_group(texture_paths)


def _get_mesh_materials(mesh_name):
    """获取网格材质"""
    materials = []
    shapes = cmds.listRelatives(mesh_name, shapes=True, noIntermediate=True) or []

    for shape in shapes:
        shading_groups = cmds.listConnections(shape, type="shadingEngine") or []
        for sg in shading_groups:
            materials_connected = cmds.listConnections(f"{sg}.surfaceShader") or []
            materials.extend([mat for mat in materials_connected if mat not in materials])

    return materials


def build_custom_topology(icon_path: str):
    custom_topo_group = face_const.CUSTOM_TOPO_GRP_NAME
    if cmds.objExists(custom_topo_group):
        cmds.delete(custom_topo_group)
    dna_path = icon_path.replace(".png", ".dna")
    custom_topo_group = cmds.group(empty=True, name=custom_topo_group)
    create_custom_topo_info_node(custom_topo_group)
    set_dna_path_to_info_node(icon_path)
    base_topo_group = cmds.group(empty=True, name=face_const.BASE_TOPO_GRP_NAME, parent=custom_topo_group)
    target_topo_group = cmds.group(empty=True, name=face_const.TARGET_TOPO_GRP_NAME, parent=custom_topo_group)
    cmds.setAttr(f"{base_topo_group}.visibility", 0)

    base_mesh = build_base_mesh(base_topo_group)
    target_mesh = build_target_mesh(target_topo_group, dna_path)
    base_mesh_visibility(False)
    wrap_target_mesh(base_mesh[0], target_mesh)
    create_blendshape_for_base_mesh()
    bake_base_mesh_tex(base_mesh[0])
    duplication_dx_shader_to_target_mesh(target_mesh)
    notify_texture_update()


def get_centerline_from_topo_info():
    node_name = face_const.CUSTOM_TOPO_INFO_NODE_NAME
    attr_name = face_const.CUSTOM_TOPO_INFO_NODE_ICON_ATTR_NAME
    full_attr_name = f"{node_name}.{attr_name}"

    if not cmds.objExists(node_name):
        return None

    if not cmds.attributeQuery(attr_name, node=node_name, exists=True):
        return None

    icon_path = cmds.getAttr(full_attr_name)

    json_path = icon_path.replace(".png", ".json")
    json_data = utils.read_json(json_path)
    return json_data.get("centerline", None)


def set_vertices_x_to_zero_by_indices(mesh_name: str, vertex_indices):
    if not cmds.objExists(mesh_name) or not vertex_indices:
        return False

    try:
        selection_list = om.MSelectionList()
        selection_list.add(mesh_name)
        dag_path = selection_list.getDagPath(0)
        mesh_fn = om.MFnMesh(dag_path)

        vertex_positions = mesh_fn.getPoints(om.MSpace.kObject)
        for vertex_index in vertex_indices:
            if vertex_index < len(vertex_positions):
                pos = vertex_positions[vertex_index]
                vertex_positions[vertex_index] = om.MPoint(0.0, pos.y, pos.z, pos.w)

        mesh_fn.setPoints(vertex_positions, om.MSpace.kObject)

        return True
    except Exception as e:
        return False


def process_mesh_symmetry(centerline_data, mesh_name):
    if not cmds.objExists(mesh_name):
        return False
    vertex_list = get_mesh_info(mesh_name)
    for vertex_index in centerline_data:
        vertex_list.remove(vertex_index)
    apply_mesh_symmetry(mesh_name, centerline_data, vertex_list)


def apply_mesh_symmetry(mesh_name: str, centerline_data: list, vertex_list: list):
    adjacent_vertices = get_random_vertex_neighbors(centerline_data, mesh_name)
    left_vertex = []
    right_vertex = []
    for vertex in adjacent_vertices:
        if vertex in vertex_list:
            vertex_pos = cmds.xform(f"{mesh_name}.vtx[{vertex}]", query=True, translation=True, worldSpace=True)

            if vertex_pos[0] > 0:
                left_vertex.append(vertex)
            else:
                right_vertex.append(vertex)

    if len(right_vertex) > 1:
        right_vertex = get_symmetry_vertices_by_distance(mesh_name, left_vertex, right_vertex)
    for i in left_vertex + right_vertex:
        if i in vertex_list:
            vertex_list.remove(i)
    all_adjacent_vertices = [left_vertex, right_vertex]
    process_recursive_symmetry(mesh_name, left_vertex, right_vertex, vertex_list, all_adjacent_vertices)


def get_symmetry_vertices_by_distance(mesh_name: str, left_vertex: list, right_vertex: list):
    """通过距离匹配对称顶点（原始方法）"""
    new_right_vertex = []
    for left_vtx in left_vertex:
        left_pos = cmds.pointPosition(f"{mesh_name}.vtx[{left_vtx}]", world=True)

        best_right = None
        min_dist = float("inf")

        for right_vtx in right_vertex:
            right_pos = cmds.pointPosition(f"{mesh_name}.vtx[{right_vtx}]", world=True)
            dist = abs(right_pos[0] + left_pos[0]) + abs(right_pos[1] - left_pos[1]) + abs(right_pos[2] - left_pos[2])

            if dist < min_dist:
                min_dist = dist
                best_right = right_vtx

        if best_right:
            new_right_vertex.append(best_right)

    return new_right_vertex


def get_symmetry_vertices_by_direction(
    mesh_name: str,
    adjacent_l: list,
    adjacent_r: list,
    left_vtx: int,
    right_vtx: int,
):
    """通过方向向量匹配对称顶点"""
    if not adjacent_l or not adjacent_r:
        return adjacent_r

    left_parent_pos = cmds.pointPosition(f"{mesh_name}.vtx[{left_vtx}]", world=True)
    right_parent_pos = cmds.pointPosition(f"{mesh_name}.vtx[{right_vtx}]", world=True)

    left_directions = []
    for adj_vtx in adjacent_l:
        adj_pos = cmds.pointPosition(f"{mesh_name}.vtx[{adj_vtx}]", world=True)
        direction = [
            adj_pos[0] - left_parent_pos[0],
            adj_pos[1] - left_parent_pos[1],
            adj_pos[2] - left_parent_pos[2],
        ]
        left_directions.append((adj_vtx, direction))

    new_right_vertex = []
    used_right = set()

    for left_adj, left_dir in left_directions:
        best_right = None
        best_score = float("inf")

        for right_adj in adjacent_r:
            if right_adj in used_right:
                continue

            right_adj_pos = cmds.pointPosition(f"{mesh_name}.vtx[{right_adj}]", world=True)

            right_direction = [
                right_adj_pos[0] - right_parent_pos[0],
                right_adj_pos[1] - right_parent_pos[1],
                right_adj_pos[2] - right_parent_pos[2],
            ]

            expected_right_dir = [-left_dir[0], left_dir[1], left_dir[2]]

            direction_diff = sum(abs(right_direction[i] - expected_right_dir[i]) for i in range(3))

            pos_diff = (
                abs(right_adj_pos[0] + left_parent_pos[0])
                + abs(right_adj_pos[1] - left_parent_pos[1])
                + abs(right_adj_pos[2] - left_parent_pos[2])
            )

            total_score = direction_diff * 2.0 + pos_diff * 0.5

            if total_score < best_score:
                best_score = total_score
                best_right = right_adj

        if best_right:
            new_right_vertex.append(best_right)
            used_right.add(best_right)

    return new_right_vertex


def apply_vertex_symmetry(mesh_name: str, all_adjacent_vertices: list):
    for left_vertex, right_vertex in zip(all_adjacent_vertices[0], all_adjacent_vertices[1]):
        left_pos = cmds.pointPosition(f"{mesh_name}.vtx[{left_vertex}]", world=True)
        cmds.move(
            -left_pos[0],
            left_pos[1],
            left_pos[2],
            f"{mesh_name}.vtx[{right_vertex}]",
            absolute=True,
            worldSpace=True,
        )


def mesh_symmetry(mesh_name: str):
    centerline_data = get_centerline_from_topo_info()
    if centerline_data:
        set_vertices_x_to_zero_by_indices(mesh_name, centerline_data)
        process_mesh_symmetry(centerline_data, mesh_name)


def get_mesh_info(mesh_name: str):
    vertex_count = cmds.polyEvaluate(mesh_name, vertex=True)
    vertex_list = list(range(vertex_count))
    return vertex_list


def get_random_vertex_neighbors(centerline_data, mesh_name):
    # Import built-in modules
    import random

    if not centerline_data:
        return None

    if not cmds.objExists(mesh_name):
        return None

    random_vertex_index = random.choice(centerline_data)
    adjacent_vertices = get_central_vertex_adjacent_vertices(mesh_name, random_vertex_index)
    return adjacent_vertices


def get_central_vertex_adjacent_vertices(mesh_name: str, vertex_index: int):
    vertex_name = f"{mesh_name}.vtx[{vertex_index}]"

    connected_edges = cmds.polyListComponentConversion(vertex_name, fromVertex=True, toEdge=True)
    if not connected_edges:
        return []

    connected_edges = cmds.ls(connected_edges, flatten=True)
    adjacent_vertices = []

    for edge in connected_edges:
        edge_vertices = cmds.polyListComponentConversion(edge, fromEdge=True, toVertex=True)
        edge_vertices = cmds.ls(edge_vertices, flatten=True)

        for vertex in edge_vertices:
            if vertex != vertex_name:
                vertex_index_adj = int(vertex.split("[")[1].split("]")[0])
                if vertex_index_adj not in adjacent_vertices:
                    adjacent_vertices.append(vertex_index_adj)

    return adjacent_vertices


def process_recursive_symmetry(
    mesh_name: str,
    left_vertex: list,
    right_vertex: list,
    vertex_list: list,
    all_adjacent_vertices: list,
):
    if not left_vertex or not right_vertex:
        return

    new_left_vertex = []
    new_right_vertex = []

    for left_vtx, right_vtx in zip(left_vertex, right_vertex):
        adjacent_l = get_central_vertex_adjacent_vertices(mesh_name, left_vtx)
        adjacent_r = get_central_vertex_adjacent_vertices(mesh_name, right_vtx)

        adjacent_l = [v for v in adjacent_l if v in vertex_list]
        adjacent_r = [v for v in adjacent_r if v in vertex_list]

        if len(adjacent_r) > 1:
            adjacent_r = get_symmetry_vertices_by_direction(mesh_name, adjacent_l, adjacent_r, left_vtx, right_vtx)

        for vertex in adjacent_l + adjacent_r:
            if vertex in vertex_list:
                vertex_list.remove(vertex)

        if adjacent_l:
            new_left_vertex.extend(adjacent_l)
        if adjacent_r:
            new_right_vertex.extend(adjacent_r)

    if len(all_adjacent_vertices) >= 2:
        all_adjacent_vertices[0].extend(new_left_vertex)
        all_adjacent_vertices[1].extend(new_right_vertex)

    if new_left_vertex and new_right_vertex and vertex_list:
        process_recursive_symmetry(mesh_name, new_left_vertex, new_right_vertex, vertex_list, all_adjacent_vertices)
    else:
        apply_vertex_symmetry(mesh_name, all_adjacent_vertices)


def eye_symmetry(left_eye_name: str, right_eye_name: str):
    if not cmds.objExists(left_eye_name) or not cmds.objExists(right_eye_name):
        return False

    try:
        right_eye_parent = cmds.listRelatives(right_eye_name, parent=True)

        cmds.delete(right_eye_name)

        duplicated_left_eye = cmds.duplicate(left_eye_name)[0]

        cmds.scale(-1, 1, 1, duplicated_left_eye, pivot=[0, 0, 0])

        new_right_eye = cmds.rename(duplicated_left_eye, right_eye_name)

        if right_eye_parent:
            cmds.parent(new_right_eye, right_eye_parent[0])

        return True

    except Exception as e:
        print(f"Error in eye_symmetry: {e}")
        return False


def eye_lashes_symmetry(eyelashes_mesh_name: str):
    if not cmds.objExists(eyelashes_mesh_name):
        return False
    vertex_count = cmds.polyEvaluate(eyelashes_mesh_name, vertex=True)

    left_vertices = []
    right_vertices = []

    for i in range(vertex_count):
        vertex_name = f"{eyelashes_mesh_name}.vtx[{i}]"
        world_pos = cmds.pointPosition(vertex_name, world=True)

        if world_pos[0] > 0:
            left_vertices.append(i)
        else:
            right_vertices.append(i)
    if right_vertices:
        right_vertex_names = [f"{eyelashes_mesh_name}.vtx[{i}]" for i in right_vertices]
        cmds.delete(right_vertex_names)

    try:
        face_count = cmds.polyEvaluate(eyelashes_mesh_name, face=True)
        faces_to_delete = []

        for i in range(face_count):
            face_name = f"{eyelashes_mesh_name}.f[{i}]"

            face_center = cmds.polyInfo(face_name, faceToVertex=True)[0]
            vertices = face_center.split()[2:]

            x_sum = 0
            vertex_count = 0
            for vtx_idx in vertices:
                vtx_name = f"{eyelashes_mesh_name}.vtx[{vtx_idx}]"
                pos = cmds.pointPosition(vtx_name, world=True)
                x_sum += pos[0]
                vertex_count += 1

            if vertex_count > 0:
                face_center_x = x_sum / vertex_count
                if face_center_x < 0:
                    faces_to_delete.append(face_name)

        if faces_to_delete:
            cmds.delete(faces_to_delete)

        duplicated_mesh = cmds.duplicate(eyelashes_mesh_name)[0]

        cmds.scale(-1, 1, 1, duplicated_mesh, pivot=[0, 0, 0])

        parent_group = cmds.listRelatives(eyelashes_mesh_name, parent=True)

        temp_name = f"{eyelashes_mesh_name}_temp"
        result = cmds.polyUnite(eyelashes_mesh_name, duplicated_mesh, name=temp_name)

        cmds.delete(result[0], constructionHistory=True)

        cmds.delete(eyelashes_mesh_name)

        if parent_group:
            cmds.parent(result[0], parent_group[0])

        final_mesh = cmds.rename(result[0], eyelashes_mesh_name)

        cmds.polyMergeVertex(final_mesh, distance=0.001)

    except Exception as e:
        return False

    return True
