"""Global Deformer UI Event Manager Module.

This module provides unified management for UI event responses related to global deformers,
separating UI event handling from UI code itself.
"""

import logging
from typing import Optional, Callable

from cgame_avatar_factory.hair_studio.data.gwrap_data import GlobalDeformerManager, GlobalDeformer


class GlobalDeformerUIEventManager:
    """统一管理变形器UI事件响应，与UI代码分离"""
    
    def __init__(self, data_manager: GlobalDeformerManager):
        self.data_manager = data_manager
        self.logger = logging.getLogger(__name__)
        
        # Maya接口回调（由外部设置，用于实际Maya操作）
        self.maya_create_curve_callback: Optional[Callable[[str], str]] = None
        self.maya_create_deformer_callback: Optional[Callable[[str, str], bool]] = None
        self.maya_select_object_callback: Optional[Callable[[str], str]] = None
        
        # UI更新回调（用于更新UI组件显示）
        self.update_hair_object_callback: Optional[Callable[[str], None]] = None
        self.update_mesh_object_callback: Optional[Callable[[str], None]] = None
        self.update_curve_data_callback: Optional[Callable[[str], None]] = None
        self.update_affected_faces_callback: Optional[Callable[[str], None]] = None
    
    def handle_select_hair_object_event(self):
        """处理选择毛发对象事件"""
        self.logger.info("处理选择毛发对象事件")
        
        selected_object = None
        if self.maya_select_object_callback:
            try:
                selected_object = self.maya_select_object_callback("hair")
                self.logger.info(f"选择毛发对象: {selected_object}")
            except Exception as e:
                self.logger.error(f"选择毛发对象失败: {e}")
        else:
            # 模拟选择（测试用）
            import random
            test_objects = [
                "hair_curve_001",
                "hair_curve_002", 
                "scalp_hair_curve_A",
                "facial_hair_curve_B"
            ]
            selected_object = random.choice(test_objects)
            self.logger.info(f"模拟选择毛发对象: {selected_object}")
        
        # 更新UI显示
        if selected_object and self.update_hair_object_callback:
            self.update_hair_object_callback(selected_object)
    
    def handle_select_mesh_object_event(self):
        """处理选择mesh对象事件"""
        self.logger.info("处理选择mesh对象事件")
        
        selected_object = None
        if self.maya_select_object_callback:
            try:
                selected_object = self.maya_select_object_callback("mesh")
                self.logger.info(f"选择mesh对象: {selected_object}")
            except Exception as e:
                self.logger.error(f"选择mesh对象失败: {e}")
        else:
            # 模拟选择（测试用）
            import random
            test_objects = [
                "hair_mesh_001",
                "hair_mesh_002",
                "scalp_mesh_A",
                "hair_geometry_B"
            ]
            selected_object = random.choice(test_objects)
            self.logger.info(f"模拟选择mesh对象: {selected_object}")
        
        # 更新UI显示
        if selected_object and self.update_mesh_object_callback:
            self.update_mesh_object_callback(selected_object)
    
    def handle_generate_curve_event(self, hair_object: str):
        """处理生成曲线事件"""
        self.logger.info(f"处理生成曲线事件: {hair_object}")
        
        if not hair_object or hair_object.strip() == "" or hair_object == "未选择毛发或面片":
            self.logger.warning("未选择有效的毛发对象")
            return
        
        curve_data = None
        if self.maya_create_curve_callback:
            try:
                curve_data = self.maya_create_curve_callback(hair_object)
                self.logger.info(f"生成曲线成功: {curve_data}")
            except Exception as e:
                self.logger.error(f"生成曲线失败: {e}")
        else:
            # 模拟生成曲线（测试用）
            import random
            curve_id = random.randint(1000, 9999)
            curve_data = f"generated_curve_{curve_id}_from_{hair_object}"
            self.logger.info(f"模拟生成曲线: {curve_data}")
        
        # 更新UI显示
        if curve_data and self.update_curve_data_callback:
            self.update_curve_data_callback(curve_data)
    
    def handle_create_driver_event(self, curve_data: str, mesh_data: str) -> bool:
        """处理创建驱动事件（原冻结按钮功能）"""
        self.logger.info(f"处理创建驱动事件: curve={curve_data}, mesh={mesh_data}")
        
        # 验证输入数据
        if not curve_data or curve_data.strip() == "" or curve_data == "未生成曲线":
            self.logger.warning("未提供有效的曲线数据")
            return False
            
        if not mesh_data or mesh_data.strip() == "" or mesh_data == "未选择mesh对象":
            self.logger.warning("未提供有效的mesh数据")
            return False
        
        # 创建变形器数据对象
        try:
            deformer_name = self.data_manager.get_next_deformer_name()
            new_deformer = GlobalDeformer(
                name=deformer_name,
                curve_data=curve_data,
                mesh_data=mesh_data
            )
            
            # 调用Maya接口创建实际变形器
            if self.maya_create_deformer_callback:
                maya_success = self.maya_create_deformer_callback(curve_data, mesh_data)
                if not maya_success:
                    self.logger.error("Maya变形器创建失败")
                    return False
            
            # 添加到数据管理器
            self.data_manager.add_deformer(new_deformer)
            self.logger.info(f"成功创建驱动: {deformer_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建驱动失败: {e}")
            return False
    
    def handle_add_driver_event(self) -> bool:
        """处理添加驱动事件（简单添加空驱动）"""
        self.logger.info("处理添加驱动事件")
        
        try:
            deformer_name = self.data_manager.get_next_deformer_name()
            new_deformer = GlobalDeformer(
                name=deformer_name,
                curve_data="",
                mesh_data=""
            )
            
            self.data_manager.add_deformer(new_deformer)
            self.logger.info(f"成功添加空驱动: {deformer_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"添加驱动失败: {e}")
            return False
    
    def handle_remove_driver_event(self, deformer_name: str) -> bool:
        """处理删除驱动事件"""
        self.logger.info(f"处理删除驱动事件: {deformer_name}")
        
        if not deformer_name:
            self.logger.warning("未提供变形器名称")
            return False
        
        try:
            removed_deformer = self.data_manager.remove_deformer_by_name(deformer_name)
            if removed_deformer:
                self.logger.info(f"成功删除驱动: {deformer_name}")
                return True
            else:
                self.logger.warning(f"未找到要删除的驱动: {deformer_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除驱动失败: {e}")
            return False
    
    def handle_deformer_selection_event(self, deformer_name: Optional[str]) -> bool:
        """处理变形器选择事件"""
        self.logger.info(f"处理变形器选择事件: {deformer_name}")
        
        try:
            self.data_manager.set_selected_deformer(deformer_name)
            self.logger.info(f"成功选择变形器: {deformer_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"选择变形器失败: {e}")
            return False
    
    def set_maya_callbacks(self, 
                          create_curve_callback: Optional[Callable[[str], str]] = None,
                          create_deformer_callback: Optional[Callable[[str, str], bool]] = None,
                          select_object_callback: Optional[Callable[[str], str]] = None):
        """设置Maya接口回调函数"""
        self.maya_create_curve_callback = create_curve_callback
        self.maya_create_deformer_callback = create_deformer_callback
        self.maya_select_object_callback = select_object_callback
        self.logger.info("Maya回调函数已设置")
    
    def handle_select_affected_faces_event(self, faces: str):
        """处理选择受影响面片事件"""
        self.logger.info("处理选择受影响面片事件")
        
        selected_deformer = self.data_manager.get_selected_deformer()
        if not selected_deformer:
            self.logger.warning("未选择变形器，无法选择受影响面片")
            return
        
        # 模拟Maya选择面片逻辑
        selected_faces = None
        if self.maya_select_object_callback:
            try:
                selected_faces = self.maya_select_object_callback("faces")
                self.logger.info(f"Maya选择面片: {selected_faces}")
            except Exception as e:
                self.logger.error(f"Maya选择面片失败: {e}")
        else:
            # 模拟选择（测试用）
            import random
            test_faces = [
                "hair_mesh_001.f[100:150]",
                "scalp_mesh_A.f[200:250]",
                "hair_geometry_B.f[50:80]",
                "hair_surface_C.f[300:350]",
                "facial_hair_mesh.f[10:30]"
            ]
            selected_faces = random.choice(test_faces)
            self.logger.info(f"模拟选择面片: {selected_faces}")
        
        # 更新UI显示
        if selected_faces and self.update_affected_faces_callback:
            self.update_affected_faces_callback(selected_faces)
        
        self.logger.info(f"为变形器 {selected_deformer.name} 选择受影响面片: {selected_faces}")
    
    def handle_set_affected_objects_event(self):
        """处理设置受影响对象事件"""
        self.logger.info("处理设置受影响对象事件")
        
        selected_deformer = self.data_manager.get_selected_deformer()
        if not selected_deformer:
            self.logger.warning("未选择变形器，无法设置受影响对象")
            return False
        
        # 这里可以添加Maya相关的对象设置逻辑
        # 目前使用模拟数据
        affected_objects = ["hair_object_1", "hair_object_2"]  # 模拟数据
        
        try:
            self.data_manager.update_deformer(selected_deformer.name, affected_objects=affected_objects)
            self.logger.info(f"成功为变形器 {selected_deformer.name} 设置受影响对象")
            return True
        except Exception as e:
            self.logger.error(f"设置受影响对象失败: {e}")
            return False
    
    def set_ui_callbacks(self,
                        update_hair_object_callback: Optional[Callable[[str], None]] = None,
                        update_mesh_object_callback: Optional[Callable[[str], None]] = None,
                        update_curve_data_callback: Optional[Callable[[str], None]] = None,
                        update_affected_faces_callback: Optional[Callable[[str], None]] = None):
        """设置UI更新回调函数"""
        self.update_hair_object_callback = update_hair_object_callback
        self.update_mesh_object_callback = update_mesh_object_callback
        self.update_curve_data_callback = update_curve_data_callback
        self.update_affected_faces_callback = update_affected_faces_callback
        self.logger.info("UI回调函数已设置")