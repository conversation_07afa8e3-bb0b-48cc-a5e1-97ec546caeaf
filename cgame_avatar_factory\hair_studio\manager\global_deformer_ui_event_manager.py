"""Global Deformer UI Event Manager Module.

This module provides unified management for UI event responses related to global deformers,
separating UI event handling from UI code itself.

Design Principles:
1. UI/Logic Separation: UI components only handle display, this manager handles business logic
2. Maya Interface Abstraction: Maya operations are abstracted through callback functions
3. Testability: Provides mock data for testing without Maya dependency
4. Modularity: Clear interfaces and single responsibility
"""

# Import built-in modules
from dataclasses import dataclass
import logging
from typing import Any
from typing import Callable
from typing import List
from typing import Optional

# Import local modules
from cgame_avatar_factory.hair_studio.data.gwrap_data import GlobalDeformer
from cgame_avatar_factory.hair_studio.data.gwrap_data import GlobalDeformerManager
from cgame_avatar_factory.hair_studio.maya_api.global_deformer import gd_maya_api


@dataclass
class EventResult:
    """事件处理结果封装"""

    success: bool
    message: str = ""
    data: Optional[Any] = None


class GlobalDeformerUIEventManager:
    """统一管理变形器UI事件响应，与UI代码分离

    优化后的设计：
    1. 使用MayaInterfaceManager统一管理Maya操作
    2. 提供EventResult统一的返回格式
    3. 支持Mock模式便于测试
    4. 清晰的UI回调接口
    """

    def __init__(self, data_manager: GlobalDeformerManager):
        self.data_manager = data_manager
        self.logger = logging.getLogger(__name__)

        # UI更新回调（用于更新UI组件显示）
        self.update_hair_object_callback: Optional[Callable[[str], None]] = None
        self.update_mesh_object_callback: Optional[Callable[[str], None]] = None
        self.update_curve_data_callback: Optional[Callable[[str], None]] = None
        self.update_affected_faces_callback: Optional[Callable[[str], None]] = None
        self.update_deformer_info_callback: Optional[Callable[[GlobalDeformer], None]] = None
        self.get_affected_objects_callback: Optional[Callable[[], List[str]]] = None
        self.clear_left_panel_callback: Optional[Callable[[], None]] = None  # 清除左侧面板数据

    def handle_select_hair_object_event(self) -> EventResult:
        """处理选择毛发对象事件"""
        self.logger.info("处理选择毛发对象事件")

        try:
            # 调用你的Maya API选择毛发面片
            selected_hair = gd_maya_api.on_select_for_curve()
            self.logger.info(f"选择毛发对象: {selected_hair} (类型: {type(selected_hair)})")

            # 处理返回值类型转换
            display_text = self._format_selection_for_display(selected_hair)

            # 更新UI显示
            if display_text and self.update_hair_object_callback:
                self.update_hair_object_callback(display_text)

            return EventResult(
                success=True,
                message="成功选择毛发对象",
                data=selected_hair,  # 保持原始数据用于后续处理
            )
        except Exception as e:
            error_msg = f"选择毛发对象失败: {e}"
            self.logger.error(error_msg)
            return EventResult(success=False, message=error_msg)

    def handle_select_mesh_object_event(self) -> EventResult:
        """处理选择mesh对象事件"""
        self.logger.info("处理选择mesh对象事件")

        try:
            # 调用你的Maya API选择要绑定的毛发mesh
            selected_mesh, selected_curve = gd_maya_api.select_hair_curve_to_bind()
            self.logger.info(f"选择mesh对象: {selected_mesh}")

            # 更新UI显示
            if selected_mesh and self.update_mesh_object_callback:
                self.update_mesh_object_callback(selected_mesh)

            return EventResult(
                success=True,
                message="成功选择mesh对象",
                data=(selected_mesh, selected_curve),
            )
        except Exception as e:
            error_msg = f"选择mesh对象失败: {e}"
            self.logger.error(error_msg)
            return EventResult(success=False, message=error_msg)

    def handle_generate_curve_event(self) -> EventResult:
        """处理生成曲线事件（合并功能：选择毛发面片 + 生成曲线）"""
        self.logger.info(f"处理合并的生成曲线事件")

        try:
            # 第一步：选择毛发面片
            select_result = self.handle_select_hair_object_event()
            if not select_result.success:
                return select_result

            # 第二步：使用选择的毛发面片生成曲线
            selected_hair = select_result.data
            self.logger.info(f"使用选择的毛发面片生成曲线: {selected_hair}")

            # 调用你的Maya API生成曲线
            curve_data = gd_maya_api.on_create_curve_btn()
            self.logger.info(f"生成曲线: {curve_data} (类型: {type(curve_data)})")

            # 处理返回值类型转换
            display_text = self._format_selection_for_display(curve_data)

            # 更新UI显示
            if display_text and self.update_curve_data_callback:
                self.update_curve_data_callback(display_text)

            return EventResult(
                success=True,
                message="成功生成曲线",
                data=curve_data,  # 保持原始数据用于后续处理
            )
        except Exception as e:
            error_msg = f"生成曲线失败: {e}"
            self.logger.error(error_msg)
            return EventResult(success=False, message=error_msg)

    def handle_create_driver_event(self) -> EventResult:
        """处理创建驱动事件（合并功能：选择头发mesh对象 + 创建驱动）"""
        self.logger.info(f"处理合并的创建驱动事件")

        try:
            # 第一步：选择头发mesh对象
            select_mesh_result = self.handle_select_mesh_object_event()
            if not select_mesh_result.success:
                return select_mesh_result

            mesh_data, curve_data = select_mesh_result.data
            self.logger.info(f"选择的mesh对象: {mesh_data}, 使用曲线数据: {curve_data}")

            # 第三步：创建变形器数据对象
            deformer_name = self.data_manager.get_next_deformer_name()

            # 创建变形器对象
            _success, new_deformer = self.data_manager.create_deformer(
                curve_data=curve_data,
                binding_mesh=mesh_data,
                name=deformer_name,
            )

            if _success and new_deformer:
                # 添加到数据管理器，这会触发UI回调
                add_success = self.data_manager.add_deformer(new_deformer)
                if add_success:
                    # 创建成功后清除左侧面板数据
                    if self.clear_left_panel_callback:
                        self.clear_left_panel_callback()
                        self.logger.info("已清除左侧面板数据")

                    return EventResult(
                        success=True,
                        message=f"成功创建驱动: {deformer_name}",
                        data=new_deformer,
                    )
                else:
                    return EventResult(
                        success=False,
                        message=f"创建驱动失败: 无法添加到管理器",
                    )
            else:
                return EventResult(
                    success=False,
                    message=f"创建驱动失败: Maya操作失败",
                )

        except Exception as e:
            error_msg = f"创建驱动失败: {e}"
            self.logger.error(error_msg)
            return EventResult(success=False, message=error_msg)

    def handle_freeze_event(self) -> bool:
        self.logger.debug(f"处理冻结事件: ")

        is_success = self.data_manager.freeze_deformers()
        if is_success:
            self.logger.info(f"成功冻结驱动")
            return True
        else:
            self.logger.error(f"冻结驱动失败")
            return False

    def handle_remove_driver_event(self, deformer_name: str) -> bool:
        """处理删除驱动事件"""
        self.logger.info(f"处理删除驱动事件: {deformer_name}")

        if not deformer_name:
            self.logger.warning("未提供变形器名称")
            return False

        removed_deformer = self.data_manager.remove_deformer(deformer_name)
        if removed_deformer:
            self.logger.info(f"成功删除驱动: {deformer_name}")
            return True

        self.logger.error(f"删除驱动失败:: {deformer_name}")
        return False

    def handle_deformer_selection_event(self, deformer_name: Optional[str]) -> bool:
        """处理变形器选择事件"""
        self.logger.info(f"处理变形器选择事件: {deformer_name}")

        try:
            # 处理空选择的情况
            if not deformer_name:
                self.data_manager.select_deformer(None)
                self.logger.info("清除变形器选择")
                return True

            # 验证变形器是否存在
            selected_deformer = self.data_manager.get_deformer_by_name(deformer_name)
            if not selected_deformer:
                self.logger.warning(f"data_manager没有找到驱动: {deformer_name}")
                return False

            # 更新数据管理器中的选择状态（传递名称，不是对象）
            success = self.data_manager.select_deformer(deformer_name)
            if success:
                self.logger.info(f"成功选择变形器: {deformer_name}")

                # 更新右侧面板信息
                if self.update_deformer_info_callback:
                    self.update_deformer_info_callback(selected_deformer)
                # select in maya, show off by select curve data
                gd_maya_api.select_deformer_data(selected_deformer.curve_data)
                return True
            else:
                self.logger.error(f"选择变形器失败: {deformer_name}")
                return False

        except Exception as e:
            self.logger.error(f"选择变形器失败: {e}")
            return False

    def _format_selection_for_display(self, selection_data) -> str:
        """格式化选择数据用于UI显示

        Args:
            selection_data: 可能是字符串、列表或其他类型的选择数据

        Returns:
            str: 格式化后的字符串，适合在UI中显示
        """
        if selection_data is None:
            return "未选择对象"

        # 如果是字符串，直接返回
        if isinstance(selection_data, str):
            return selection_data

        # 如果是列表或元组
        if isinstance(selection_data, (list, tuple)):
            if len(selection_data) == 0:
                return "未选择对象"
            elif len(selection_data) == 1:
                return str(selection_data[0])
            else:
                # 多个选择项，显示数量和前几个项目
                if len(selection_data) <= 3:
                    return ", ".join(str(item) for item in selection_data)
                else:
                    first_items = ", ".join(str(item) for item in selection_data[:2])
                    return f"{first_items} 等 {len(selection_data)} 个对象"

        # 其他类型，转换为字符串
        return str(selection_data)

    def handle_select_affected_faces_event(self) -> EventResult:
        """处理选择受影响面片事件"""
        self.logger.info("处理选择受影响面片事件")

        try:
            # 调用你的Maya API选择受影响的面片
            affected_faces = gd_maya_api.select_hair_face_set_weight()
            self.logger.info(f"选择受影响面片: {affected_faces} (类型: {type(affected_faces)})")

            # 处理返回值类型转换
            display_text = self._format_selection_for_display(affected_faces)

            # 更新UI显示（传递显示文本和原始数据）
            if display_text and self.update_affected_faces_callback:
                # 检查回调函数是否支持raw_faces参数
                # Import built-in modules
                import inspect

                callback_signature = inspect.signature(self.update_affected_faces_callback)
                if "raw_faces" in callback_signature.parameters:
                    # 新版本：传递原始数据
                    raw_faces = affected_faces if isinstance(affected_faces, list) else []
                    self.update_affected_faces_callback(display_text, raw_faces=raw_faces)
                else:
                    # 向后兼容：只传递显示文本
                    self.update_affected_faces_callback(display_text)

            return EventResult(
                success=True,
                message="成功选择受影响面片",
                data=affected_faces,  # 保持原始数据用于后续处理
            )
        except Exception as e:
            error_msg = f"选择受影响面片失败: {e}"
            self.logger.error(error_msg)
            return EventResult(success=False, message=error_msg)

    def handle_set_affected_objects_event(self) -> EventResult:
        """处理设置受影响对象事件（合并功能：选择受影响的面片 + 设置影响面片）"""
        self.logger.info("处理合并的设置受影响对象事件")

        try:
            # 第一步：选择受影响的面片
            select_result = self.handle_select_affected_faces_event()
            if not select_result.success:
                return select_result

            affected_faces = select_result.data
            self.logger.info(f"选择的受影响面片: {affected_faces}")

            # 第二步：设置影响面片到选中的变形器
            selected_deformer = self.data_manager.get_selected_deformer()
            if not selected_deformer:
                return EventResult(
                    success=False,
                    message="未选择变形器，无法设置受影响对象",
                )

            # 处理面片数据格式
            affected_mesh_faces = affected_faces if isinstance(affected_faces, list) else [affected_faces] if affected_faces else []

            if not affected_mesh_faces:
                return EventResult(
                    success=False,
                    message="未找到有效的受影响面片数据",
                )

            # 设置到变形器
            self.data_manager.update_deformer(selected_deformer.name, affected_mesh_faces=affected_mesh_faces)
            self.logger.info(f"成功为变形器 {selected_deformer.name} 设置受影响对象: {affected_mesh_faces}")

            return EventResult(
                success=True,
                message=f"成功设置受影响对象",
                data=affected_mesh_faces,
            )

        except Exception as e:
            error_msg = f"设置受影响对象失败: {e}"
            self.logger.error(error_msg)
            return EventResult(success=False, message=error_msg)

    def set_ui_callbacks(
        self,
        update_hair_object_callback: Optional[Callable[[str], None]] = None,
        update_mesh_object_callback: Optional[Callable[[str], None]] = None,
        update_curve_data_callback: Optional[Callable[[str], None]] = None,
        update_affected_faces_callback: Optional[Callable[[str], None]] = None,
        update_deformer_info_callback: Optional[Callable[[GlobalDeformer], None]] = None,
        get_affected_objects_callback: Optional[Callable[[], List[str]]] = None,
        clear_left_panel_callback: Optional[Callable[[], None]] = None,
    ):
        """设置UI更新回调函数"""
        self.update_hair_object_callback = update_hair_object_callback
        self.update_mesh_object_callback = update_mesh_object_callback
        self.update_curve_data_callback = update_curve_data_callback
        self.update_affected_faces_callback = update_affected_faces_callback
        self.update_deformer_info_callback = update_deformer_info_callback
        self.get_affected_objects_callback = get_affected_objects_callback
        self.clear_left_panel_callback = clear_left_panel_callback
        self.logger.info("UI回调函数已设置")
