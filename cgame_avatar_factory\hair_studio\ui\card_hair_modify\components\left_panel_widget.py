"""Left Panel Widget Module.

This module provides the left panel widget for curve generation and mesh binding.
"""

import logging
import random
from typing import Optional

# Import Qt modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import dayu widgets
from dayu_widgets import <PERSON>Label, MLineEdit, MPushButton

# Import local modules
from cgame_avatar_factory.hair_studio.constants import DEFAULT_MARGIN, DEFAULT_SPACING


class LeftPanelWidget(QtWidgets.QWidget):
    """左侧面板组件：曲线生成和mesh绑定"""
    
    # 定义信号
    curve_generated = QtCore.Signal(str)  # 生成的曲线数据
    hair_object_selected = QtCore.Signal(str)  # 选中的毛发对象
    mesh_object_selected = QtCore.Signal(str)  # 选中的mesh对象
    driver_create_requested = QtCore.Signal(str, str)  # 请求创建驱动 (curve_data, mesh_data)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._logger = logging.getLogger(__name__)
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """设置UI布局"""
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setSpacing(DEFAULT_SPACING)
        main_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)
        
        # 创建两个组，垂直方向2:1比例
        curve_group = self.create_curve_generation_section()
        binding_group = self.create_mesh_binding_section()
        
        # 添加到主布局，设置拉伸因子
        main_layout.addWidget(curve_group, 2)  # 2/3的空间
        main_layout.addWidget(binding_group, 1)  # 1/3的空间
    
    def create_curve_generation_section(self):
        """创建曲线生成区域"""
        curve_group = QtWidgets.QGroupBox("生成曲线 Generate Curve")
        curve_layout = QtWidgets.QVBoxLayout(curve_group)
        curve_layout.setSpacing(DEFAULT_SPACING)
        curve_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)
        
        # Hair object selection button
        self.select_hair_btn = MPushButton("请选择毛发面片来生成曲线")
        curve_layout.addWidget(self.select_hair_btn)

        # Hair object display label - shows selected object
        self.hair2curve_label = QtWidgets.QTextEdit()
        self.hair2curve_label.setPlaceholderText("未选择毛发或面片")
        self.hair2curve_label.setReadOnly(True)
        self.hair2curve_label.setMaximumHeight(50)
        curve_layout.addWidget(self.hair2curve_label)

        curve_layout.addStretch()
        
        # Generated curve data display label
        curve_data_title = MLabel("生成的曲线数据：")
        curve_layout.addWidget(curve_data_title)
        
        self.generated_curve_label = QtWidgets.QTextEdit()
        self.generated_curve_label.setPlaceholderText("未生成曲线")
        self.generated_curve_label.setReadOnly(True)
        self.generated_curve_label.setMaximumHeight(50)
        curve_layout.addWidget(self.generated_curve_label)

        # Generate curve button
        self.generate_curve_btn = MPushButton("生成曲线>>")
        curve_layout.addWidget(self.generate_curve_btn, alignment=QtCore.Qt.AlignRight)
        
        # Add stretch to push content to top
        curve_layout.addStretch()
        
        return curve_group
    
    def create_mesh_binding_section(self):
        """创建mesh绑定区域"""
        binding_group = QtWidgets.QGroupBox("创建约束 Create Binding")
        binding_layout = QtWidgets.QVBoxLayout(binding_group)
        binding_layout.setSpacing(DEFAULT_SPACING)
        binding_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)
        
        # Mesh object selection button
        self.select_mesh_btn = MPushButton("请选择头发mesh对象")
        binding_layout.addWidget(self.select_mesh_btn)

        # Mesh object display label - shows selected object
        self.mesh_selection_label = QtWidgets.QTextEdit()
        self.mesh_selection_label.setPlaceholderText("未选择mesh对象")
        self.mesh_selection_label.setReadOnly(True)
        self.mesh_selection_label.setMaximumHeight(50)
        binding_layout.addWidget(self.mesh_selection_label)

        # Binding button
        self.binding_btn = MPushButton("创建驱动\u003e\u003e")
        binding_layout.addWidget(self.binding_btn, alignment= QtCore.Qt.AlignRight)
        
        # Add stretch to push content to top
        binding_layout.addStretch()
        
        return binding_group
    
    def setup_connections(self):
        """设置信号连接"""
        self.select_hair_btn.clicked.connect(self.on_select_hair_object)
        self.select_mesh_btn.clicked.connect(self.on_select_mesh_object)
        self.generate_curve_btn.clicked.connect(self.on_generate_curve)
        self.binding_btn.clicked.connect(self.on_create_driver)
    
    def on_select_hair_object(self):
        """处理选择毛发对象按钮点击"""
        print(f"[EVENT] Select Hair Object button clicked")
        # 只触发信号，不包含业务逻辑
        self.hair_object_selected.emit("")

    def on_select_mesh_object(self):
        """处理选择mesh对象按钮点击"""
        print(f"[EVENT] Select Mesh Object button clicked")
        # 只触发信号，不包含业务逻辑
        self.mesh_object_selected.emit("")

    def on_generate_curve(self):
        """处理生成曲线按钮点击"""
        print(f"[EVENT] Generate Curve button clicked")
        
        # 获取选中的毛发对象
        hair_object = self.hair2curve_label.toPlainText().strip()
        print(f"[EVENT] Hair object from label: '{hair_object}'")
        
        # 只触发信号，让manager处理业务逻辑
        self.curve_generated.emit(hair_object)

    def on_create_driver(self):
        """处理创建驱动按钮点击（原冻结按钮功能）"""
        print(f"[EVENT] Create Driver button clicked")
        
        # 获取曲线数据和mesh数据
        curve_data = self.get_generated_curve_data().strip()
        mesh_data = self.get_selected_mesh_data().strip()
        
        print(f"[EVENT] Curve data: '{curve_data}'")
        print(f"[EVENT] Mesh data: '{mesh_data}'")
        
        # 只触发信号，让manager处理验证和业务逻辑
        self.driver_create_requested.emit(curve_data, mesh_data)


    
    def get_generated_curve_data(self) -> str:
        """获取生成的曲线数据"""
        return self.generated_curve_label.toPlainText()
    
    def get_selected_mesh_data(self) -> str:
        """获取选中的mesh数据"""
        return self.mesh_selection_label.toPlainText()
    
    # UI更新方法（供manager回调使用）
    def update_hair_object_display(self, hair_object: str):
        """更新毛发对象显示"""
        print(f"[UI] Updating hair object display: {hair_object}")
        self.hair2curve_label.setPlainText(hair_object)
    
    def update_mesh_object_display(self, mesh_object: str):
        """更新mesh对象显示"""
        print(f"[UI] Updating mesh object display: {mesh_object}")
        self.mesh_selection_label.setPlainText(mesh_object)
    
    def update_generated_curve_display(self, curve_data: str):
        """更新生成的曲线数据显示"""
        print(f"[UI] Updating generated curve display: {curve_data}")
        self.generated_curve_label.setPlainText(curve_data)
