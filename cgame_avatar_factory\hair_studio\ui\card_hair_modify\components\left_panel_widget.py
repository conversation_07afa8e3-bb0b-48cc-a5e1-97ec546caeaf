"""Left Panel Widget Module.

This module provides the left panel widget for curve generation and mesh binding.
"""

# Import built-in modules
import logging

# Import third-party modules
# Import dayu widgets
from dayu_widgets import MPushButton

# Import Qt modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.constants import DEFAULT_MARGIN
from cgame_avatar_factory.hair_studio.constants import DEFAULT_SPACING


class LeftPanelWidget(QtWidgets.QWidget):
    """左侧面板组件：曲线生成和mesh绑定"""

    # 定义信号
    curve_generated = QtCore.Signal(str)  # 生成的曲线数据
    hair_object_selected = QtCore.Signal(str)  # 选中的毛发对象
    mesh_object_selected = QtCore.Signal(str)  # 选中的mesh对象
    driver_create_requested = QtCore.Signal(str, str)  # 请求创建驱动 (curve_data, mesh_data)

    def __init__(self, parent=None):
        super().__init__(parent)
        self._logger = logging.getLogger(__name__)
        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """设置UI布局"""
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setSpacing(DEFAULT_SPACING)
        main_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN / 2, DEFAULT_MARGIN / 2)

        # 创建两个组，垂直方向2:1比例
        curve_group = self.create_curve_generation_section()
        binding_group = self.create_mesh_binding_section()

        # 添加到主布局，设置拉伸因子
        main_layout.addWidget(curve_group, 1)  # 2/3的空间
        main_layout.addWidget(binding_group, 1)  # 1/3的空间



    def create_curve_generation_section(self):
        """创建曲线生成区域"""
        curve_group = QtWidgets.QGroupBox("生成曲线")
        curve_layout = QtWidgets.QVBoxLayout(curve_group)
        curve_layout.setSpacing(DEFAULT_SPACING)
        curve_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)

        curve_layout.addStretch()

        # Generate curve button (合并功能：选择毛发面片 + 生成曲线)
        self.generate_curve_btn = MPushButton("生成曲线\u003e\u003e")
        curve_layout.addWidget(self.generate_curve_btn, alignment=QtCore.Qt.AlignRight)

        # Add stretch to push content to top
        curve_layout.addStretch()

        return curve_group

    def create_mesh_binding_section(self):
        """创建mesh绑定区域"""
        binding_group = QtWidgets.QGroupBox("创建约束")
        binding_layout = QtWidgets.QVBoxLayout(binding_group)
        binding_layout.setSpacing(DEFAULT_SPACING)
        binding_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)

        # Binding button (合并功能：选择头发mesh对象 + 创建驱动)
        self.binding_btn = MPushButton("创建驱动\u003e\u003e")
        binding_layout.addWidget(self.binding_btn, alignment=QtCore.Qt.AlignRight)

        # Add stretch to push content to top
        binding_layout.addStretch()

        return binding_group

    def setup_connections(self):
        """设置信号连接"""
        self.generate_curve_btn.clicked.connect(self.on_generate_curve)
        self.binding_btn.clicked.connect(self.on_create_driver)

    def on_generate_curve(self):
        """处理生成曲线按钮点击（合并功能：选择毛发面片 + 生成曲线）"""
        print(f"[EVENT] Generate Curve button clicked (merged functionality)")

        # 先触发选择毛发对象的信号，然后触发生成曲线的信号
        # UI事件管理器会处理这个串联逻辑
        self.hair_object_selected.emit("")

    def on_create_driver(self):
        """处理创建驱动按钮点击（合并功能：选择头发mesh对象 + 创建驱动）"""
        print(f"[EVENT] Create Driver button clicked (merged functionality)")

        # 先触发选择mesh对象的信号，然后触发创建驱动的信号
        # UI事件管理器会处理这个串联逻辑
        self.mesh_object_selected.emit("")

    # UI更新方法（供manager回调使用）
    def clear_all_data(self):
        """清除所有数据，用于创建驱动成功后重置界面"""
        print("[UI] Clearing all data")
