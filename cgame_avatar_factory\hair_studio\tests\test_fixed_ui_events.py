#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test Fixed UI Events Module.

This module tests the fixed UI event handling to ensure proper signal connections.
"""

import sys
import logging
from qtpy import QtWidgets, QtCore

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s - %(name)s - %(message)s'
)

# Import the main widget
from cgame_avatar_factory.hair_studio.ui.card_hair_modify.global_deformer_widget import GlobalDeformerWidget


class TestWindow(QtWidgets.QMainWindow):
    """Test window for UI event verification"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Fixed UI Events Test")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create the main widget
        self.deformer_widget = GlobalDeformerWidget()
        self.setCentralWidget(self.deformer_widget)
        
        # Add status bar for feedback
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("Ready - Click buttons to test UI events")
        
        # Connect to external signals for verification
        self.setup_verification_connections()
    
    def setup_verification_connections(self):
        """Setup connections to verify events are working"""
        # Connect to the widget's external signals
        self.deformer_widget.deformer_added.connect(self.on_deformer_added)
        self.deformer_widget.deformer_removed.connect(self.on_deformer_removed)
        self.deformer_widget.deformer_selected.connect(self.on_deformer_selected)
    
    def on_deformer_added(self, deformer):
        """Handle deformer added signal"""
        message = f"✅ Deformer Added: {deformer.name}"
        print(f"[VERIFICATION] {message}")
        self.status_bar.showMessage(message, 3000)
    
    def on_deformer_removed(self, deformer_name):
        """Handle deformer removed signal"""
        message = f"✅ Deformer Removed: {deformer_name}"
        print(f"[VERIFICATION] {message}")
        self.status_bar.showMessage(message, 3000)
    
    def on_deformer_selected(self, deformer_name):
        """Handle deformer selected signal"""
        message = f"✅ Deformer Selected: {deformer_name}"
        print(f"[VERIFICATION] {message}")
        self.status_bar.showMessage(message, 3000)


def main():
    """Main function to run the test"""
    app = QtWidgets.QApplication(sys.argv)
    
    print("=" * 60)
    print("FIXED UI EVENTS TEST")
    print("=" * 60)
    print("Instructions:")
    print("1. Click '请选择毛发面片来生成曲线' - Should update hair object display")
    print("2. Click '请选择头发mesh对象' - Should update mesh object display")
    print("3. Click '生成曲线>>' - Should generate and display curve data")
    print("4. Click '创建驱动>>' - Should create a new deformer")
    print("5. Click '冻结' - Should add an empty deformer")
    print("6. Select items in the list and click '删除' - Should remove deformer")
    print("=" * 60)
    
    # Create and show the test window
    window = TestWindow()
    window.show()
    
    # Run the application
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()