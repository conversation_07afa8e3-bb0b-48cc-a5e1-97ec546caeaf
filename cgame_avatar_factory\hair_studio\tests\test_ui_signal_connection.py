#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test UI Signal Connection to GlobalDeformerUIEventManager

This test verifies that UI signals are properly connected to the event manager.
"""

import sys
import logging
from qtpy import QtWidgets, QtCore

# Import the main widget and components
from cgame_avatar_factory.hair_studio.ui.card_hair_modify.global_deformer_widget import GlobalDeformerWidget


class TestUISignalConnection(QtWidgets.QMainWindow):
    """测试UI信号连接的主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setup_logging()
        self.setup_ui()
        
        self.setWindowTitle("Test UI Signal Connection to Event Manager")
        self.resize(1000, 700)
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("=== UI Signal Connection Test Started ===")
    
    def setup_ui(self):
        """设置UI"""
        # 创建主要的变形器组件
        self.deformer_widget = GlobalDeformerWidget()
        self.setCentralWidget(self.deformer_widget)
        
        # 添加测试按钮
        self.create_test_buttons()
        
        self.logger.info("UI setup completed")
    
    def create_test_buttons(self):
        """创建测试按钮"""
        # 创建测试面板
        test_panel = QtWidgets.QWidget()
        test_layout = QtWidgets.QVBoxLayout(test_panel)
        
        # 添加说明标签
        info_label = QtWidgets.QLabel("点击下面的按钮来测试UI信号连接:")
        info_label.setStyleSheet("font-weight: bold; color: blue; margin: 10px;")
        test_layout.addWidget(info_label)
        
        # 测试按钮
        test_buttons = [
            ("测试选择毛发对象", self.test_select_hair_object),
            ("测试选择Mesh对象", self.test_select_mesh_object),
            ("测试生成曲线", self.test_generate_curve),
            ("测试创建驱动", self.test_create_driver),
            ("测试添加驱动", self.test_add_driver),
            ("测试删除驱动", self.test_remove_driver),
        ]
        
        for button_text, callback in test_buttons:
            btn = QtWidgets.QPushButton(button_text)
            btn.clicked.connect(callback)
            btn.setMinimumHeight(30)
            test_layout.addWidget(btn)
        
        # 添加到主布局
        main_layout = self.deformer_widget.layout()
        if main_layout:
            main_layout.addWidget(test_panel)
    
    def test_select_hair_object(self):
        """测试选择毛发对象"""
        self.logger.info("=== Testing Hair Object Selection ===")
        # 模拟点击选择毛发对象按钮
        self.deformer_widget.left_panel.select_hair_btn.click()
    
    def test_select_mesh_object(self):
        """测试选择Mesh对象"""
        self.logger.info("=== Testing Mesh Object Selection ===")
        # 模拟点击选择mesh对象按钮
        self.deformer_widget.left_panel.select_mesh_btn.click()
    
    def test_generate_curve(self):
        """测试生成曲线"""
        self.logger.info("=== Testing Curve Generation ===")
        # 先确保有毛发对象
        self.deformer_widget.left_panel.hair2curve_label.setPlainText("test_hair_object")
        # 模拟点击生成曲线按钮
        self.deformer_widget.left_panel.generate_curve_btn.click()
    
    def test_create_driver(self):
        """测试创建驱动"""
        self.logger.info("=== Testing Driver Creation ===")
        # 先设置必要的数据
        self.deformer_widget.left_panel.generated_curve_label.setPlainText("test_curve_data")
        self.deformer_widget.left_panel.mesh_selection_label.setPlainText("test_mesh_data")
        # 模拟点击创建驱动按钮
        self.deformer_widget.left_panel.binding_btn.click()
    
    def test_add_driver(self):
        """测试添加驱动"""
        self.logger.info("=== Testing Add Driver ===")
        # 模拟点击冻结按钮（添加驱动）
        self.deformer_widget.middle_panel.freeze_btn.click()
    
    def test_remove_driver(self):
        """测试删除驱动"""
        self.logger.info("=== Testing Remove Driver ===")
        # 先添加一个驱动项
        self.deformer_widget.middle_panel.drivers_list.addItem("test_driver_001")
        self.deformer_widget.middle_panel.drivers_list.setCurrentRow(0)
        # 模拟点击删除按钮
        self.deformer_widget.middle_panel.remove_driver_btn.click()


def main():
    """主函数"""
    app = QtWidgets.QApplication(sys.argv)
    
    # 创建测试窗口
    test_window = TestUISignalConnection()
    test_window.show()
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()