# 毛发模块功能概述

> 面向对外提交（PR）的精简版说明：聚焦核心功能、组件职责、关键接口、历史修复要点与协作方式。避免指向本地 debug 文档。

## 1. 核心功能

- 资产库与配置
  - 卡片类(Card)资产库 Tab 配置与动态路径发现（大小写不敏感、别名支持）
  - 统一常量配置（路径、Tab 显示、支持的文件格式、Shader 映射）
- 数据与组件管理
  - 从配置路径加载资产（携带子类型）；组件的创建/删除/更新/选择
  - 组件可见性与 UI 增量更新信号
- Maya 集成
  - 基于资产创建发型节点、删除节点、设置可见性、选择组件
  - 材质分配/替换：配置驱动创建 Shader 并自动绑定
- Shader 配置系统
  - 注册、获取、覆盖、预设；资产类型 → 配置映射；可扩展、可测试
- UI 结构
  - 顶层 HairStudioTab（Card/XGen/Curve 三类 Tab），基础 Tab 组合编辑区/组件列表/资产库三大区域

## 2. 各组件职责

| 组件/模块 | 职责概要 |
|---|---|
| manager/HairManager | 组件生命周期管理（新增/删除/更新/选择/可见），向 UI 派发信号，调用 Maya API；安全删除机制 |
| data/DataManager | 扫描资产（按子类型路径载入）、维护内存态组件（Mock 友好） |
| maya_api/* | create/delete/select/visibility、Shader 创建与分配、资产导入（FBX/OBJ、轴向检测与对齐） |
| config/shader_configs.py | Shader 配置注册/查询/覆盖/预设；集中管理配置与校验 |
| ui/* | HairStudioTab 容器与 BaseHairTab 基础框架，组合编辑区/组件列表/资产库 |
| constants.py | 全局常量（资产库路径、Tab 映射/显示、Shader 映射、支持格式、UI 文案） |

## 3. 关键接口（精选）

- 组件管理（manager/hair_manager.py）
  - add_component_from_asset(asset_id)
  - delete_component(component_id)
  - set_component_visibility(component_id, is_visible)
  - select_component(component_id)
- Maya API（maya_api/hair_operations.py）
  - create_hair_node(asset_data) -> {success, node_names}
  - delete_node(node_names_dict)
  - set_node_visibility(node_names_dict, is_visible)
  - select_component_nodes(node_names_dict)
- 材质与 Shader（maya_api/assign_shader.py）
  - assign_material_to_mesh(mesh_name, texture_path, shader_name=None, asset_type="hair", shader_engine=None)
  - replace_shading_node(obj_name, shader_mapping, texture_path, shader_name=None)
- Shader 配置（config/__init__.py）
  - get_shader_config, register_shader_config, create_config_with_overrides, get_preset_config
- 数据访问（data/data_manager.py）
  - get_assets(asset_type=None), reload_assets()

## 4. 精简的历史修复/优化

| 区域 | 问题 | 方案 | 价值 |
|---|---|---|---|
| Shader 系统 | 硬编码、代码重复、扩展性弱 | 配置化与管理器重构；统一创建与替换流程 | 降低耦合、易扩展、易测试 |
| 资产库配置 | Tab/路径硬编码 | 引入 AssetLibConfig；动态路径发现与别名匹配 | 维护性与扩展性提升 |
| 节点数据结构 | 单节点字符串不足 | 统一使用 node_names 字典（严格类型） | 多节点能力、一致性更强 |
| 安全删除 | 直接 del 导致 KeyError/状态遗漏 | _safe_remove_component（pop/状态清理/信号/日志） | 更稳健的生命周期管理 |
| 数据加载 | 子类型重复解析 | 由配置直传子类型，优化扫描函数 | 性能与清晰度提升 |
| 资产导入 | 仅 OBJ、轴向不统一 | 支持 FBX/OBJ 检测与轴向对齐 | 兼容更多资产，减少人工处理 |

## 5. 协作建议

- 改动 Shader 或资产路径：优先修改 constants 与配置层（AssetLibConfig / shader_configs），避免分散硬编码
- 引入新的 Maya 能力：同步完善对应的 Mock 与测试用例
- 新增子类型/资产类型：先补充常量与配置，再扩展 DataManager 的加载路径
- 提交前自检：
  - 日志记录在类初始化或函数内定义（logger = logging.getLogger(__name__))
  - 关键逻辑有最小单元测试，测试使用 Mock 数据
  - 文档最小化、接口变更附简要迁移说明

## 6. 兼容性与测试（摘要）

- 对外 API 保持稳定；配置化重构保证旧调用可运作
- 测试采用 Mock 策略，运行快速、可重复；建议在 CI 中运行核心 Mock 测试集

— 完 —
