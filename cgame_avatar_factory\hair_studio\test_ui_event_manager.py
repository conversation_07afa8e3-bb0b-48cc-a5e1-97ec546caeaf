"""Test script for GlobalDeformerUIEventManager and component fixes.

This script tests the UI event manager and verifies that component access issues are fixed.
"""

import sys
import logging
from qtpy import QtWidgets, QtCore

# Import the components
from cgame_avatar_factory.hair_studio.data.gwrap_data import GlobalDeformerManager
from cgame_avatar_factory.hair_studio.manager.global_deformer_ui_event_manager import GlobalDeformerUIEventManager
from cgame_avatar_factory.hair_studio.ui.card_hair_modify.components.left_panel_widget import LeftPanelWidget
from cgame_avatar_factory.hair_studio.ui.card_hair_modify.components.middle_panel_widget import MiddlePanelWidget


class TestMainWindow(QtWidgets.QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setup_logging()
        self.setup_managers()
        self.setup_ui()
        self.setup_connections()
        
        self.setWindowTitle("Global Deformer UI Event Manager Test")
        self.resize(800, 600)
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_managers(self):
        """设置管理器"""
        # 创建数据管理器
        self.data_manager = GlobalDeformerManager()
        
        # 创建UI事件管理器
        self.ui_event_manager = GlobalDeformerUIEventManager(self.data_manager)
        
        # 设置数据管理器的回调
        self.data_manager.on_deformer_added = self.on_deformer_added
        self.data_manager.on_deformer_removed = self.on_deformer_removed
        self.data_manager.on_selection_changed = self.on_selection_changed
        
        self.logger.info("管理器初始化完成")
    
    def setup_ui(self):
        """设置UI"""
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QtWidgets.QHBoxLayout(central_widget)
        
        # 左侧面板
        self.left_panel = LeftPanelWidget()
        layout.addWidget(self.left_panel, 1)
        
        # 中间面板
        self.middle_panel = MiddlePanelWidget()
        layout.addWidget(self.middle_panel, 1)
        
        # 右侧状态显示
        self.status_text = QtWidgets.QTextEdit()
        self.status_text.setReadOnly(True)
        self.status_text.setMaximumWidth(300)
        layout.addWidget(self.status_text)
        
        self.add_status("UI初始化完成")
    
    def setup_connections(self):
        """设置信号连接"""
        # 左侧面板信号连接到UI事件管理器
        self.left_panel.hair_object_selected.connect(self.on_hair_object_selected)
        self.left_panel.mesh_object_selected.connect(self.on_mesh_object_selected)
        self.left_panel.curve_generated.connect(self.on_curve_generated)
        self.left_panel.driver_create_requested.connect(self.on_driver_create_requested)
        
        # 中间面板信号连接到UI事件管理器
        self.middle_panel.add_deformer_requested.connect(self.on_add_deformer_requested)
        self.middle_panel.remove_deformer_requested.connect(self.on_remove_deformer_requested)
        self.middle_panel.deformer_selection_changed.connect(self.on_deformer_selection_changed)
        
        self.add_status("信号连接完成")
    
    def on_hair_object_selected(self, hair_object: str):
        """处理毛发对象选择"""
        self.add_status(f"选择毛发对象: {hair_object}")
    
    def on_mesh_object_selected(self, mesh_object: str):
        """处理mesh对象选择"""
        self.add_status(f"选择mesh对象: {mesh_object}")
    
    def on_curve_generated(self, curve_data: str):
        """处理曲线生成"""
        self.add_status(f"生成曲线: {curve_data}")
    
    def on_driver_create_requested(self, curve_data: str, mesh_data: str):
        """处理创建驱动请求（原冻结按钮功能）"""
        self.add_status(f"请求创建驱动: curve={curve_data}, mesh={mesh_data}")
        
        # 调用UI事件管理器处理
        success = self.ui_event_manager.handle_create_driver_event(curve_data, mesh_data)
        if success:
            self.add_status("✓ 驱动创建成功")
        else:
            self.add_status("✗ 驱动创建失败")
    
    def on_add_deformer_requested(self):
        """处理添加变形器请求"""
        self.add_status("请求添加变形器")
        
        # 调用UI事件管理器处理
        success = self.ui_event_manager.handle_add_driver_event()
        if success:
            self.add_status("✓ 变形器添加成功")
        else:
            self.add_status("✗ 变形器添加失败")
    
    def on_remove_deformer_requested(self):
        """处理删除变形器请求"""
        current_deformer = self.middle_panel.get_current_deformer_name()
        self.add_status(f"请求删除变形器: {current_deformer}")
        
        if current_deformer:
            # 调用UI事件管理器处理
            success = self.ui_event_manager.handle_remove_driver_event(current_deformer)
            if success:
                self.add_status("✓ 变形器删除成功")
            else:
                self.add_status("✗ 变形器删除失败")
        else:
            self.add_status("✗ 未选择要删除的变形器")
    
    def on_deformer_selection_changed(self, current, previous):
        """处理变形器选择变化"""
        current_name = current.text() if current else None
        self.add_status(f"变形器选择变化: {current_name}")
        
        # 调用UI事件管理器处理
        self.ui_event_manager.handle_deformer_selection_event(current_name)
    
    def on_deformer_added(self, deformer):
        """数据管理器回调：变形器已添加"""
        self.add_status(f"数据回调: 变形器已添加 - {deformer.name}")
        self.middle_panel.add_deformer_to_list(deformer)
    
    def on_deformer_removed(self, deformer_name: str):
        """数据管理器回调：变形器已删除"""
        self.add_status(f"数据回调: 变形器已删除 - {deformer_name}")
        self.middle_panel.remove_deformer_from_list(deformer_name)
    
    def on_selection_changed(self, deformer_name):
        """数据管理器回调：选择已变化"""
        self.add_status(f"数据回调: 选择变化 - {deformer_name}")
    
    def add_status(self, message: str):
        """添加状态信息"""
        self.status_text.append(f"[{QtCore.QTime.currentTime().toString()}] {message}")
        self.logger.info(message)


def main():
    """主函数"""
    app = QtWidgets.QApplication(sys.argv)
    
    window = TestMainWindow()
    window.show()
    
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())