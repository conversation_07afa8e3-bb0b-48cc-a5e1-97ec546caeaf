"""Property Slider Widget Module.

This module provides a reusable property slider widget that combines
a label, slider, and value display in a single component.
"""

# Import Qt modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import dayu widgets
from dayu_widgets import <PERSON><PERSON>abe<PERSON>, MSlider


class PropertySliderWidget(QtWidgets.QWidget):
    """封装的属性滑条组件：左侧label + 中间滑条 + 右侧数值"""
    
    # 定义信号
    valueChanged = QtCore.Signal(int)
    
    def __init__(self, label_text: str, min_value: int = 0, max_value: int = 100, 
                 default_value: int = 50, parent=None):
        super().__init__(parent)
        self.setup_ui(label_text, min_value, max_value, default_value)
        self.setup_connections()
    
    def setup_ui(self, label_text: str, min_value: int, max_value: int, default_value: int):
        """设置UI布局"""
        layout = QtWidgets.QHBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 左侧标签
        self.label = MLabel(label_text)
        layout.addWidget(self.label)
        
        # 中间滑条
        self.slider = MSlider()
        self.slider.setOrientation(QtCore.Qt.Horizontal)
        self.slider.setRange(min_value, max_value)
        self.slider.setValue(default_value)
        layout.addWidget(self.slider, 1)  # 拉伸因子为1
        
        # 右侧数值显示
        self.value_label = MLabel(str(default_value))
        self.value_label.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(self.value_label)
    
    def setup_connections(self):
        """设置信号连接"""
        self.slider.valueChanged.connect(self._on_value_changed)
    
    def _on_value_changed(self, value):
        """处理滑条值变化"""
        self.value_label.setText(str(value))
        self.valueChanged.emit(value)
    
    def setValue(self, value: int):
        """设置滑条值"""
        self.slider.setValue(value)
    
    def getValue(self) -> int:
        """获取滑条值"""
        return self.slider.value()
    
    def setRange(self, min_value: int, max_value: int):
        """设置滑条范围"""
        self.slider.setRange(min_value, max_value)
