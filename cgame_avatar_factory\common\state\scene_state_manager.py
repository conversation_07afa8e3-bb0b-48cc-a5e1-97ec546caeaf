# Import built-in modules
import json

# Import third-party modules
import maya.cmds as cmds

# Import local modules
from cgame_avatar_factory.common import constants as const

# -------------------------
# Usage Example
# -------------------------
# 1. Automatically load on project startup
# import scene_state_manager as ssm
#
# 2. Save information for each module
# ssm.set_section("dna_list", dna_list)
# ssm.set_section("area_weights", area_weights)
# ssm.set_section("my_custom_module", {"foo": 123, "bar": "abc"})
#
# 3. Restore information for each module
# dna_list = ssm.get_section("dna_list", [])
# area_weights = ssm.get_section("area_weights", {})
#
# 4. Automatically save when closing the window
# def closeEvent(self, event):
#     ssm.save()
#     super().closeEvent(event)

# -------------------------
# Module-level state variables
# -------------------------
_state = {}
_loaded = False


def set_section(section_name, data):
    """Save information for a specific module/section.

    Args:
        section_name (str): The name of the section (e.g., 'dna_list').
        data (Any): The data to store for this section.
    """
    global _state
    _state[section_name] = data


def get_section(section_name, default=None):
    """Retrieve information for a specific module/section.

    Args:
        section_name (str): The name of the section.
        default (Any, optional): The default value if section is not found.

    Returns:
        Any: The stored data or default.
    """
    return _state.get(section_name, default)


def remove_section(section_name):
    """Remove a section from the state.

    Args:
        section_name (str): The name of the section to remove.
    """
    global _state
    if section_name in _state:
        del _state[section_name]


def get_all():
    """Get a copy of the entire state dictionary.

    Returns:
        dict: A copy of the current state.
    """
    return _state.copy()


def set_all(state_dict):
    """Replace the entire state with a new dictionary.

    Args:
        state_dict (dict): The new state dictionary.
    """
    global _state
    _state = dict(state_dict)


# --- Load/Save ---
def load():
    """Load state information from the Maya scene (network node)."""
    global _state, _loaded
    state = _load_from_storage()
    if state:
        _state = state
        _loaded = True


def save():
    """Save state information to the Maya scene (network node)."""
    _save_to_storage(_state)


def _get_network_node():
    """Get or create the network node used for storing state.

    Returns:
        str: The name of the network node.
    """
    if not cmds.objExists(const.NETWORK_NODE_NAME):
        node = cmds.createNode("network", name=const.NETWORK_NODE_NAME)
    else:
        node = const.NETWORK_NODE_NAME
    return node


def _save_to_storage(state_dict):
    """Serialize and save the state dictionary to the network node.

    Args:
        state_dict (dict): The state to save.
    """
    node = _get_network_node()
    state_json = json.dumps(state_dict)
    if not cmds.attributeQuery(const.SCENE_STATE_ATTR_NAME, node=node, exists=True):
        cmds.addAttr(node, longName=const.SCENE_STATE_ATTR_NAME, dataType="string")
    cmds.setAttr(f"{node}.{const.SCENE_STATE_ATTR_NAME}", state_json, type="string")


def _load_from_storage():
    """Load and deserialize the state dictionary from the network node.

    Returns:
        dict or None: The loaded state, or None if not found or invalid.
    """
    node = const.NETWORK_NODE_NAME
    if not cmds.objExists(node):
        return None
    if not cmds.attributeQuery(const.SCENE_STATE_ATTR_NAME, node=node, exists=True):
        return None
    state_json = cmds.getAttr(f"{node}.{const.SCENE_STATE_ATTR_NAME}")
    try:
        return json.loads(state_json)
    except Exception:
        return None


def clear():
    """Clear all state information and delete the network node from the scene."""
    global _state
    _state = {}
    node = const.NETWORK_NODE_NAME
    if cmds.objExists(node):
        cmds.delete(node)


# -------------------------
# Module initialization
# -------------------------
# Automatically load state when module is imported
load()
