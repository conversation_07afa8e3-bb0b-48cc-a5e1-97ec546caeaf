#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Global Deformer Widget Module (Refactored).

This module provides the main widget for global deformation controls.
Refactored to use separate component modules for better maintainability.
"""

import logging
from typing import Optional

# Import Qt modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.constants import DEFAULT_MARGIN, DEFAULT_SPACING
from cgame_avatar_factory.hair_studio.data.gwrap_data import (
    GlobalDeformer,
    GlobalDeformerManager,
)
from cgame_avatar_factory.hair_studio.ui.card_hair_modify.components import (
    LeftPanelWidget,
    MiddlePanelWidget,
    RightPanelWidget,
)
from cgame_avatar_factory.hair_studio.constants import GLOBAL_DEFORMER_STYLE
from cgame_avatar_factory.hair_studio.manager.global_deformer_ui_event_manager import GlobalDeformerUIEventManager
class GlobalDeformerWidget(QtWidgets.QWidget):
    """Global Deformer Widget (Refactored).

    This widget provides global deformation controls for hair components.
    It uses separate component modules for better code organization.
    """
    
    # Define signals as class attributes
    deformer_added = QtCore.Signal(GlobalDeformer)
    deformer_removed = QtCore.Signal(str)  # deformer_name
    deformer_selected = QtCore.Signal(str)  # deformer_name
    deformer_updated = QtCore.Signal(GlobalDeformer)

    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        # Initialize data management first
        self.init_data_management()

        # Initialize UI
        self.setup_ui()
        
        self.setStyleSheet(GLOBAL_DEFORMER_STYLE)

    def setup_ui(self):
        """Setup the main UI layout using component modules."""
        # Create main horizontal layout
        main_layout = QtWidgets.QHBoxLayout(self)
        main_layout.setSpacing(DEFAULT_SPACING)
        main_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)

        # Create component panels
        self.left_panel = LeftPanelWidget()
        self.middle_panel = MiddlePanelWidget()
        self.right_panel = RightPanelWidget()

        # Add panels to main layout
        main_layout.addWidget(self.left_panel)
        main_layout.addWidget(self.middle_panel, 1)  # Stretch factor 1 for expansion
        main_layout.addWidget(self.right_panel)

        # Setup signal connections between components
        self.setup_connections()
        
        # Set up UI callbacks after UI creation
        self.ui_event_manager.set_ui_callbacks(
            update_hair_object_callback=self.left_panel.update_hair_object_display,
            update_mesh_object_callback=self.left_panel.update_mesh_object_display,
            update_curve_data_callback=self.left_panel.update_generated_curve_display,
            update_affected_faces_callback=self.right_panel.update_affected_faces_display
        )
        
        # Set up Maya callbacks (can be overridden by external code)
        self.ui_event_manager.set_maya_callbacks(
            create_curve_callback=self._default_create_curve_callback,
            create_deformer_callback=self._default_create_deformer_callback,
            select_object_callback=self._default_select_object_callback
        )


    def init_data_management(self):
        """Initialize data management for the widget."""
        # Initialize global deformer manager
        self.deformer_manager = GlobalDeformerManager()

        # Initialize UI event manager
        self.ui_event_manager = GlobalDeformerUIEventManager(self.deformer_manager)

        # Set up UI callbacks for data manager
        self.deformer_manager.on_deformer_added = self._on_data_deformer_added
        self.deformer_manager.on_deformer_removed = self._on_data_deformer_removed
        self.deformer_manager.on_deformer_updated = self._on_data_deformer_updated
        self.deformer_manager.on_selection_changed = self._on_data_selection_changed
        
        # UI callbacks will be set after UI creation in setup_ui()

    def setup_connections(self):
        """Setup signal connections between components."""
        # Left panel connections - connect to UI event manager
        self.left_panel.hair_object_selected.connect(
            self.ui_event_manager.handle_select_hair_object_event
        )
        self.left_panel.mesh_object_selected.connect(
            self.ui_event_manager.handle_select_mesh_object_event
        )
        self.left_panel.curve_generated.connect(
            self.ui_event_manager.handle_generate_curve_event
        )
        self.left_panel.driver_create_requested.connect(
            self.ui_event_manager.handle_create_driver_event
        )

        # Middle panel connections - connect to UI event manager
        self.middle_panel.add_deformer_requested.connect(
            lambda: self.ui_event_manager.handle_add_driver_event()
        )
        self.middle_panel.remove_deformer_requested.connect(
            lambda: self.ui_event_manager.handle_remove_driver_event(
                self.middle_panel.get_current_deformer_name()
            )
        )
        self.middle_panel.deformer_selection_changed.connect(
            lambda current, previous: self.ui_event_manager.handle_deformer_selection_event(
                current.text() if current else None
            )
        )

        # Right panel connections - connect to UI event manager
        self.right_panel.rebuild_segments_changed.connect(self.on_rebuild_segments_changed)
        self.right_panel.influence_range_changed.connect(self.on_influence_range_changed)
        self.right_panel.affected_faces_selected.connect(
            lambda faces: self.ui_event_manager.handle_select_affected_faces_event(faces)
        )
        self.right_panel.affected_objects_set.connect(
            lambda: self.ui_event_manager.handle_set_affected_objects_event()
        )

    # Data Manager Callbacks (UI updates from data changes)
    def _on_data_deformer_added(self, deformer: GlobalDeformer):
        """Handle deformer added from data manager."""
        print(f"[UI] Data callback: Deformer added - {deformer.name}")
        
        # Add to middle panel list
        self.middle_panel.add_deformer_to_list(deformer)
        
        # Emit external signal
        self.deformer_added.emit(deformer)

    def _on_data_deformer_removed(self, deformer_name: str):
        """Handle deformer removed from data manager."""
        print(f"[UI] Data callback: Deformer removed - {deformer_name}")
        
        # Remove from middle panel list
        self.middle_panel.remove_deformer_from_list(deformer_name)
        
        # Emit external signal
        self.deformer_removed.emit(deformer_name)

    def _on_data_deformer_updated(self, deformer: GlobalDeformer):
        """Handle deformer updated from data manager."""
        print(f"[UI] Data callback: Deformer updated - {deformer.name}")
        
        # Update right panel if this is the selected deformer
        selected_deformer = self.deformer_manager.get_selected_deformer()
        if selected_deformer and selected_deformer.name == deformer.name:
            self.right_panel.update_deformer_info(deformer)
        
        # Emit external signal
        self.deformer_updated.emit(deformer)

    def _on_data_selection_changed(self, deformer_name: Optional[str]):
        """Handle selection changed from data manager."""
        display_name = deformer_name or "None"
        print(f"[UI] Data callback: Selection changed to - {display_name}")
        
        # Update UI selection
        if deformer_name is None:
            self.middle_panel.clear_selection()
            self.right_panel.update_deformer_info(None)
        else:
            deformer = self.deformer_manager.get_deformer_by_name(deformer_name)
            if deformer:
                # Update middle panel selection
                self.middle_panel.select_deformer_by_name(deformer.name)
                
                # Update right panel info
                self.right_panel.update_deformer_info(deformer)
        
        # Emit external signal
        if deformer_name:
            self.deformer_selected.emit(deformer_name)

    # Event Handlers for component signals - Now handled by UI Event Manager
    # These methods are kept for backward compatibility but are no longer used

    def on_rebuild_segments_changed(self, value):
        """Handle rebuild segments change from right panel."""
        print(f"[EVENT] Rebuild segments changed to: {value}")
        
        selected_deformer = self.deformer_manager.get_selected_deformer()
        if selected_deformer:
            print(f"[EVENT] Updating rebuild segments for deformer: {selected_deformer.name}")
            self.deformer_manager.update_deformer(selected_deformer.name, rebuild_segments=value)
        else:
            print(f"[EVENT] No deformer selected, ignoring rebuild segments change")

    def on_influence_range_changed(self, value):
        """Handle influence range change from right panel."""
        print(f"[EVENT] Influence range changed to: {value}")
        
        selected_deformer = self.deformer_manager.get_selected_deformer()
        if selected_deformer:
            print(f"[EVENT] Updating influence range for deformer: {selected_deformer.name}")
            self.deformer_manager.update_deformer(selected_deformer.name, influence_range=float(value))
        else:
            print(f"[EVENT] No deformer selected, ignoring influence range change")

    def on_affected_faces_selected(self, faces: str):
        """Handle affected faces selected from right panel."""
        print(f"[EVENT] Affected faces selected: {faces}")

    def on_set_affected_objects(self):
        """Handle set affected objects from right panel."""
        print(f"[EVENT] Set affected objects requested")
        
        selected_deformer = self.deformer_manager.get_selected_deformer()
        if not selected_deformer:
            print(f"[EVENT] Warning: No deformer selected")
            self._logger.warning("请先选择一个变形器")
            return
        
        # This would typically get the objects from the right panel
        # For now, we'll use placeholder logic
        affected_objects = ["hair_object_1", "hair_object_2"]  # Placeholder
        
        print(f"[EVENT] Setting affected objects for deformer: {selected_deformer.name}")
        self.deformer_manager.update_deformer(selected_deformer.name, affected_objects=affected_objects)
        
        self._logger.info(f"Set affected objects for deformer {selected_deformer.name}")

    # Public API methods
    def get_selected_deformer(self) -> Optional[GlobalDeformer]:
        """Get the currently selected deformer."""
        return self.deformer_manager.get_selected_deformer()

    def get_deformer_count(self) -> int:
        """Get the total number of deformers."""
        return len(self.deformer_manager.get_all_deformers())
    
    def set_maya_callbacks(self, 
                          create_curve_callback=None,
                          create_deformer_callback=None,
                          select_object_callback=None):
        """设置Maya接口回调函数（公共API）"""
        self.ui_event_manager.set_maya_callbacks(
            create_curve_callback=create_curve_callback,
            create_deformer_callback=create_deformer_callback,
            select_object_callback=select_object_callback
        )
        self._logger.info("Maya回调函数已更新")
    
    # Default Maya callback methods (fallback implementations)
    def _default_create_curve_callback(self, hair_object: str) -> str:
        """默认创建曲线回调（模拟实现）"""
        import random
        curve_id = random.randint(1000, 9999)
        result = f"generated_curve_{curve_id}_from_{hair_object}"
        self._logger.info(f"[DEFAULT] 模拟创建曲线: {result}")
        return result
    
    def _default_create_deformer_callback(self, curve_data: str, mesh_data: str) -> bool:
        """默认创建变形器回调（模拟实现）"""
        self._logger.info(f"[DEFAULT] 模拟创建变形器: curve={curve_data}, mesh={mesh_data}")
        return True
    
    def _default_select_object_callback(self, object_type: str) -> str:
        """默认选择对象回调（模拟实现）"""
        import random
        if object_type == "hair":
            test_objects = ["hair_curve_001", "hair_curve_002", "scalp_hair_curve_A"]
        elif object_type == "mesh":
            test_objects = ["hair_mesh_001", "hair_mesh_002", "scalp_mesh_A"]
        else:
            test_objects = ["unknown_object"]
        
        result = random.choice(test_objects)
        self._logger.info(f"[DEFAULT] 模拟选择{object_type}对象: {result}")
        return result
