#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Global Deformer Widget Module (Refactored).

This module provides the main widget for global deformation controls.
Refactored to use separate component modules for better maintainability.
"""

# Import built-in modules
import logging
from typing import Optional

# Import third-party modules
# Import Qt modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.constants import DEFAULT_SPACING
from cgame_avatar_factory.hair_studio.constants import GLOBAL_DEFORMER_STYLE
from cgame_avatar_factory.hair_studio.data.gwrap_data import GlobalDeformer
from cgame_avatar_factory.hair_studio.data.gwrap_data import GlobalDeformerManager
from cgame_avatar_factory.hair_studio.manager.global_deformer_ui_event_manager import GlobalDeformerUIEventManager
from cgame_avatar_factory.hair_studio.maya_api.global_deformer.interactive_menu import AllViewportObjectMenuManager
from cgame_avatar_factory.hair_studio.ui.card_hair_modify.components import LeftPanelWidget
from cgame_avatar_factory.hair_studio.ui.card_hair_modify.components import MiddlePanelWidget
from cgame_avatar_factory.hair_studio.ui.card_hair_modify.components import RightPanelWidget


class GlobalDeformerWidget(QtWidgets.QWidget):
    """Global Deformer Widget (Refactored).

    This widget provides global deformation controls for hair components.
    It uses separate component modules for better code organization.
    """

    # Define signals as class attributes
    deformer_added = QtCore.Signal(GlobalDeformer)
    deformer_removed = QtCore.Signal(str)  # deformer_name
    deformer_selected = QtCore.Signal(str)  # deformer_name
    deformer_updated = QtCore.Signal(GlobalDeformer)

    def __init__(self, parent=None):
        super().__init__(parent)

        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        self.menu_manager = AllViewportObjectMenuManager()

        # Initialize data management first
        self.init_data_management()

        # Initialize UI
        self.setup_ui()

        self.setStyleSheet(GLOBAL_DEFORMER_STYLE)

    def setup_ui(self):
        """Setup the main UI layout using component modules."""
        # Create main vertical layout
        main_vertical_layout = QtWidgets.QVBoxLayout(self)

        # Create horizontal layout for the three panels
        panels_layout = QtWidgets.QHBoxLayout()
        panels_layout.setSpacing(DEFAULT_SPACING)
        # panels_layout.setContentsMargins(0, 8, 0, 8)

        # Create component panels
        self.left_panel = LeftPanelWidget()
        self.middle_panel = MiddlePanelWidget()
        self.right_panel = RightPanelWidget()

        # Add panels to horizontal layout
        panels_layout.addWidget(self.left_panel, 1)
        panels_layout.addWidget(self.middle_panel, 1)  # Stretch factor 1 for expansion
        panels_layout.addWidget(self.right_panel, 1)

        # Add horizontal layout to main vertical layout
        main_vertical_layout.addLayout(panels_layout)

        # Add spacer to push panels to top
        spacer = QtWidgets.QSpacerItem(20, 800, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        main_vertical_layout.addItem(spacer)

        # Setup signal connections between components
        self.setup_connections()

        # Set up UI event manager callbacks (UI -> Manager -> UI updates)
        self._setup_ui_event_callbacks()

    def init_data_management(self):
        """Initialize data management for the widget."""
        # Initialize global deformer manager
        self.deformer_manager = GlobalDeformerManager()

        # Initialize UI event manager
        self.ui_event_manager = GlobalDeformerUIEventManager(self.deformer_manager)

        # Set up data manager callbacks (data -> UI notifications)
        self.deformer_manager.on_deformer_added = self._on_data_deformer_added
        self.deformer_manager.on_deformer_removed = self._on_data_deformer_removed
        self.deformer_manager.on_deformer_updated = self._on_data_deformer_updated
        self.deformer_manager.on_selection_changed = self._on_data_selection_changed

    def setup_connections(self):
        """Setup signal connections between components."""
        # Left panel connections - connect to UI event manager
        # 合并后的按钮功能：
        # - hair_object_selected 信号现在由 "生成曲线" 按钮触发，会先选择毛发面片再生成曲线
        # - mesh_object_selected 信号现在由 "创建驱动>>" 按钮触发，会先选择mesh对象再创建驱动
        self.left_panel.hair_object_selected.connect(
            lambda: self.ui_event_manager.handle_generate_curve_event(),
        )
        self.left_panel.mesh_object_selected.connect(
            lambda: self.ui_event_manager.handle_create_driver_event(),
        )

        # Middle panel connections - connect to UI event manager
        self.middle_panel.on_freeze_requested.connect(
            lambda: self.ui_event_manager.handle_freeze_event(),
        )
        self.middle_panel.remove_deformer_requested.connect(
            lambda: self.ui_event_manager.handle_remove_driver_event(
                self.middle_panel.get_current_deformer_name(),
            ),
        )
        self.middle_panel.deformer_selection_changed.connect(
            lambda current, previous: self.ui_event_manager.handle_deformer_selection_event(
                current.text() if current else None,
            ),
        )

        # Right panel connections - connect to UI event manager
        self.right_panel.curvature_changed.connect(self.on_curvature_changed)
        self.right_panel.influence_range_changed.connect(self.on_influence_range_changed)
        # 合并后的按钮功能：
        # - selecte_affected_faces 信号现在由 "设置影响面片" 按钮触发，会先选择受影响面片再设置影响面片
        self.right_panel.selecte_affected_faces.connect(
            lambda: self.ui_event_manager.handle_set_affected_objects_event(),
        )

    # Data Manager Callbacks (UI updates from data changes)
    def _on_data_deformer_added(self, deformer: GlobalDeformer):
        """Handle deformer added from data manager."""
        self._logger.info(f"[UI] Data callback: Deformer added - {deformer.name}")

        # Add to middle panel list
        self.middle_panel.add_deformer_to_list(deformer)

        # Emit external signal
        self.deformer_added.emit(deformer)

    def _on_data_deformer_removed(self, deformer_name: str):
        """Handle deformer removed from data manager."""
        self._logger.info(f"[UI] Data callback: Deformer removed - {deformer_name}")

        # Remove from middle panel list
        self.middle_panel.remove_deformer_from_list(deformer_name)

        # Emit external signal
        self.deformer_removed.emit(deformer_name)

    def _on_data_deformer_updated(self, deformer: GlobalDeformer):
        """Handle deformer updated from data manager."""
        self._logger.info(f"[UI] Data callback: Deformer updated - {deformer.name}")

        # Update right panel if this is the selected deformer
        selected_deformer = self.deformer_manager.get_selected_deformer()
        if selected_deformer and selected_deformer.name == deformer.name:
            self.right_panel.update_deformer_info(deformer)

        # Emit external signal
        self.deformer_updated.emit(deformer)

    def _on_data_selection_changed(self, deformer_name: Optional[str]):
        """Handle selection changed from data manager."""
        display_name = deformer_name or "None"
        self._logger.info(f"[UI] Data callback: Selection changed to - {display_name}")

        # Update UI selection
        if deformer_name is None:
            self.middle_panel.clear_selection()
            self.right_panel.update_deformer_info(None)
        else:
            deformer = self.deformer_manager.get_deformer_by_name(deformer_name)
            if deformer:
                # Update middle panel selection
                self.middle_panel.select_deformer_by_name(deformer.name)

                # Update right panel info
                self.right_panel.update_deformer_info(deformer)

        # Emit external signal
        if deformer_name:
            self.deformer_selected.emit(deformer_name)

    # Event Handlers for component signals - Now handled by UI Event Manager
    # These methods are kept for backward compatibility but are no longer used

    def on_curvature_changed(self, value):
        """Handle rebuild curvature change from right panel."""
        self._logger.debug(f"[EVENT] Rebuild curvature changed to: {value}")

        selected_deformer = self.deformer_manager.get_selected_deformer()
        if selected_deformer:
            self._logger.debug(f"[EVENT] Updating rebuild curvature for deformer: {selected_deformer.name}")
            self.deformer_manager.update_deformer(selected_deformer.name, curvature=value)
        else:
            self._logger.warning(f"[EVENT] No deformer selected, ignoring rebuild curvature change")

    def on_influence_range_changed(self, value):
        """Handle influence range change from right panel."""
        self._logger.debug(f"[EVENT] Influence range changed to: {value}")

        selected_deformer = self.deformer_manager.get_selected_deformer()
        if selected_deformer:
            self._logger.debug(f"[EVENT] Updating influence range for deformer: {selected_deformer.name}")
            self.deformer_manager.update_deformer(selected_deformer.name, influence_range=float(value))
        else:
            self._logger.warning(f"[EVENT] No deformer selected, ignoring influence range change")



    # Public API methods
    def get_selected_deformer(self) -> Optional[GlobalDeformer]:
        """Get the currently selected deformer."""
        return self.deformer_manager.get_selected_deformer()

    def get_deformer_count(self) -> int:
        """Get the total number of deformers."""
        return len(self.deformer_manager.get_all_deformers())

    def _setup_ui_event_callbacks(self):
        """设置UI事件管理器的回调函数"""
        # Set up UI update callbacks (Manager -> UI)
        # 注意：由于合并了按钮功能，一些UI更新回调不再需要，因为不再有对应的显示组件
        self.ui_event_manager.set_ui_callbacks(
            update_affected_faces_callback=self.right_panel.update_affected_faces_display,
            update_deformer_info_callback=self.right_panel.update_deformer_info,
            get_affected_objects_callback=self.right_panel.get_affected_objects_from_ui,
            clear_left_panel_callback=self.left_panel.clear_all_data,  # 添加清除左侧面板数据的回调
        )

        self._logger.info("UI事件管理器回调函数已设置")

    def showEvent(self, event):
        """显示时绑定菜单"""
        super().showEvent(event)
        if hasattr(self, "menu_manager") and self.menu_manager:
            if not self.ui_event_manager:
                return
            self.menu_manager.bind(self.ui_event_manager)
            self._logger.debug("[显示] 菜单绑定成功")

    def hideEvent(self, event):
        """隐藏时解绑菜单"""
        super().hideEvent(event)
        if hasattr(self, "menu_manager") and self.menu_manager:
            self._logger.debug("[隐藏] 菜单解绑成功")
            self.menu_manager.unbind()

    def closeEvent(self, event):
        """关闭时清理资源"""
        self._logger.info("[关闭] 组件关闭，清理资源")
        self.hide()  # 触发hideEvent解绑菜单
        super().closeEvent(event)
