"""Global Wrapper Data Classes Module.

This module provides data classes and state management for global deformation operations.
"""

# Import built-in modules
from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Callable
from enum import Enum
import uuid
import logging


class CurveDriverStatus(Enum):
    """Status of curve driver."""
    INACTIVE = "inactive"
    ACTIVE = "active"
    ERROR = "error"
    PROCESSING = "processing"


@dataclass
class GlobalDeformer:
    """Global Deformer class - each item in the curve driver list."""
    name: str  # 变形器名字，作为唯一ID
    curve_data: str = ""  # 曲线数据
    binding_mesh: str = ""  # 绑定mesh数据
    status: CurveDriverStatus = CurveDriverStatus.ACTIVE
    rebuild_segments: int = 15
    influence_range: float = 15.0
    affected_objects: List[str] = field(default_factory=list)
    created_time: str = ""

    def __post_init__(self):
        if not self.created_time:
            import datetime
            self.created_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'name': self.name,
            'curve_data': self.curve_data,
            'binding_mesh': self.binding_mesh,
            'status': self.status.value,
            'rebuild_segments': self.rebuild_segments,
            'influence_range': self.influence_range,
            'affected_objects': self.affected_objects.copy(),
            'created_time': self.created_time
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GlobalDeformer':
        """Create from dictionary."""
        return cls(
            name=data['name'],
            curve_data=data.get('curve_data', ''),
            binding_mesh=data.get('binding_mesh', ''),
            status=CurveDriverStatus(data.get('status', 'active')),
            rebuild_segments=data.get('rebuild_segments', 15),
            influence_range=data.get('influence_range', 15.0),
            affected_objects=data.get('affected_objects', []),
            created_time=data.get('created_time', '')
        )

    def get_display_info(self) -> str:
        """Get display information for debugging."""
        return (f"Name: {self.name}\n"
                f"Curve: {self.curve_data or 'None'}\n"
                f"Mesh: {self.binding_mesh or 'None'}\n"
                f"Status: {self.status.value}\n"
                f"Segments: {self.rebuild_segments}\n"
                f"Range: {self.influence_range}\n"
                f"Objects: {len(self.affected_objects)}\n"
                f"Created: {self.created_time}")

# Keep CurveDriverData for backward compatibility (will be removed later)
CurveDriverData = GlobalDeformer


# GlobalDeformerState removed - functionality moved to GlobalDeformerManager


class GlobalDeformerManager:
    """Manager class for GlobalDeformer operations with UI/Data separation."""

    def __init__(self):
        # Direct storage of deformers
        self.deformers: List[GlobalDeformer] = []
        self.selected_deformer_name: Optional[str] = None
        self.logger = logging.getLogger(__name__)

        # Callbacks for UI updates (to be set by UI components)
        self.on_deformer_added: Optional[Callable[[GlobalDeformer], None]] = None
        self.on_deformer_removed: Optional[Callable[[str], None]] = None  # deformer_name
        self.on_deformer_updated: Optional[Callable[[GlobalDeformer], None]] = None
        self.on_selection_changed: Optional[Callable[[Optional[str]], None]] = None  # deformer_name
    
    def get_deformer_by_name(self, name: str) -> Optional[GlobalDeformer]:
        """Get deformer by name."""
        for deformer in self.deformers:
            if deformer.name == name:
                return deformer
        return None

    def remove_deformer_by_name(self, name: str) -> Optional[GlobalDeformer]:
        """Remove and return deformer by name."""
        for i, deformer in enumerate(self.deformers):
            if deformer.name == name:
                return self.deformers.pop(i)
        return None

    def get_next_deformer_name(self) -> str:
        """Generate next available deformer name."""
        base_name = "deformer"
        existing_numbers = []

        for deformer in self.deformers:
            if deformer.name.startswith(base_name):
                try:
                    num_part = deformer.name[len(base_name):]
                    if num_part.isdigit():
                        existing_numbers.append(int(num_part))
                except:
                    pass

        next_num = 1
        while next_num in existing_numbers:
            next_num += 1

        return f"{base_name}{next_num}"

    def is_name_available(self, name: str) -> bool:
        """Check if deformer name is available."""
        return self.get_deformer_by_name(name) is None

    def create_deformer(self, curve_data: str = "", binding_mesh: str = "", name: str = "") -> GlobalDeformer:
        """Create a new global deformer."""
        deformer_name = name if name and self.is_name_available(name) else self.get_next_deformer_name()

        deformer = GlobalDeformer(
            name=deformer_name,
            curve_data=curve_data,
            binding_mesh=binding_mesh,
            status=CurveDriverStatus.ACTIVE
        )

        self.logger.info(f"[DATA] Created new deformer: {deformer_name}")
        return deformer
    
    def add_deformer(self, deformer: GlobalDeformer) -> bool:
        """Add deformer and notify UI."""
        try:
            # Check for name conflicts
            if self.get_deformer_by_name(deformer.name):
                self.logger.warning(f"[DATA] Deformer name '{deformer.name}' already exists")
                return False

            self.deformers.append(deformer)
            self.logger.info(f"[DATA] Added deformer: {deformer.name}")

            # Notify UI
            if self.on_deformer_added:
                self.on_deformer_added(deformer)

            return True
        except Exception as e:
            self.logger.error(f"[DATA] Failed to add deformer: {e}")
            return False
    
    def remove_deformer(self, deformer_name: str) -> bool:
        """Remove deformer and notify UI."""
        try:
            removed_deformer = self.remove_deformer_by_name(deformer_name)
            if not removed_deformer:
                self.logger.warning(f"[DATA] Deformer not found for removal: {deformer_name}")
                return False

            self.logger.info(f"[DATA] Removed deformer: {removed_deformer.name}")

            # Clear selection if this was selected
            if self.selected_deformer_name == deformer_name:
                self.selected_deformer_name = None
                if self.on_selection_changed:
                    self.on_selection_changed(None)

            # Notify UI
            if self.on_deformer_removed:
                self.on_deformer_removed(deformer_name)

            return True
        except Exception as e:
            self.logger.error(f"[DATA] Failed to remove deformer: {e}")
            return False
    
    def update_deformer(self, deformer_name: str, **kwargs) -> bool:
        """Update deformer data and notify UI."""
        try:
            deformer = self.get_deformer_by_name(deformer_name)
            if not deformer:
                self.logger.warning(f"[DATA] Deformer not found for update: {deformer_name}")
                return False

            # Update attributes
            updated_fields = []
            for key, value in kwargs.items():
                if hasattr(deformer, key):
                    old_value = getattr(deformer, key)
                    setattr(deformer, key, value)
                    updated_fields.append(f"{key}: {old_value} -> {value}")
                else:
                    self.logger.warning(f"[DATA] Invalid attribute: {key}")

            if updated_fields:
                self.logger.info(f"[DATA] Updated deformer {deformer.name}: {', '.join(updated_fields)}")

                # Notify UI
                if self.on_deformer_updated:
                    self.on_deformer_updated(deformer)

                return True

            return False
        except Exception as e:
            self.logger.error(f"[DATA] Failed to update deformer: {e}")
            return False
    
    def select_deformer(self, deformer_name: Optional[str]) -> bool:
        """Select a deformer and notify UI."""
        try:
            if deformer_name and not self.get_deformer_by_name(deformer_name):
                self.logger.warning(f"[DATA] Cannot select non-existent deformer: {deformer_name}")
                return False

            old_selection = self.selected_deformer_name
            self.selected_deformer_name = deformer_name

            if old_selection != deformer_name:
                display_name = deformer_name or "None"
                self.logger.info(f"[DATA] Selection changed to: {display_name}")

                # Notify UI
                if self.on_selection_changed:
                    self.on_selection_changed(deformer_name)

            return True
        except Exception as e:
            self.logger.error(f"[DATA] Failed to select deformer: {e}")
            return False
    
    def get_selected_deformer(self) -> Optional[GlobalDeformer]:
        """Get currently selected deformer."""
        if self.selected_deformer_name:
            return self.get_deformer_by_name(self.selected_deformer_name)
        return None

    def get_all_deformers(self) -> List[GlobalDeformer]:
        """Get all deformers."""
        return self.deformers.copy()

    def clear_all_deformers(self) -> bool:
        """Clear all deformers."""
        try:
            count = len(self.deformers)
            self.deformers.clear()
            self.selected_deformer_name = None

            self.logger.info(f"[DATA] Cleared {count} deformers")

            # Notify UI
            if self.on_selection_changed:
                self.on_selection_changed(None)

            return True
        except Exception as e:
            self.logger.error(f"[DATA] Failed to clear deformers: {e}")
            return False

    def rename_deformer(self, old_name: str, new_name: str) -> bool:
        """Rename a deformer (simple implementation)."""
        try:
            if not self.is_name_available(new_name):
                self.logger.warning(f"[DATA] Name '{new_name}' is already taken")
                return False

            deformer = self.get_deformer_by_name(old_name)
            if not deformer:
                self.logger.warning(f"[DATA] Deformer not found for rename: {old_name}")
                return False

            deformer.name = new_name

            # Update selection if this was selected
            if self.selected_deformer_name == old_name:
                self.selected_deformer_name = new_name

            self.logger.info(f"[DATA] Renamed deformer: {old_name} -> {new_name}")

            # Notify UI
            if self.on_deformer_updated:
                self.on_deformer_updated(deformer)

            return True
        except Exception as e:
            self.logger.error(f"[DATA] Failed to rename deformer: {e}")
            return False


# Keep old class name for backward compatibility (will be removed later)
CurveDriverManager = GlobalDeformerManager
