#!/usr/bin/env python
"""Core test for reference asset functionality - validates the new reference field feature."""

# Import built-in modules
import os
import shutil
import tempfile
from unittest.mock import MagicMock
from unittest.mock import patch


def test_find_reference_asset_core_logic():
    """Test the core logic of _find_reference_asset function."""
    print("Testing _find_reference_asset core logic...")

    test_dir = None
    try:
        # Import built-in modules
        import logging

        # Import local modules
        from cgame_avatar_factory.hair_studio.data.data_manager import _find_reference_asset

        # Create minimal test directory structure
        test_dir = tempfile.mkdtemp(prefix="ref_test_")

        # Create hair asset directory
        hair_dir = os.path.join(test_dir, "char1")
        os.makedirs(hair_dir)

        # Create hair model
        hair_model = os.path.join(hair_dir, "char1.fbx")
        with open(hair_model, "w") as f:
            f.write("# Hair model")

        # Test case 1: No reference found
        logger = logging.getLogger(__name__)
        result = _find_reference_asset(hair_model, logger)
        assert result is None, "Should return None when no reference found"
        print("   ✓ Correctly returns None when no reference found")

        # Test case 2: Reference in same directory
        head_model = os.path.join(hair_dir, "char1_head.obj")
        with open(head_model, "w") as f:
            f.write("# Head model")

        result = _find_reference_asset(hair_model, logger)
        assert result is not None, "Should find reference in same directory"
        assert result == head_model, f"Should return correct path: {head_model}"
        print("   ✓ Correctly finds reference in same directory")

        # Test case 3: Reference in parent directory
        os.remove(head_model)  # Remove local reference
        parent_head = os.path.join(test_dir, "shared_head.fbx")
        with open(parent_head, "w") as f:
            f.write("# Shared head model")

        result = _find_reference_asset(hair_model, logger)
        assert result is not None, "Should find reference in parent directory"
        assert result == parent_head, f"Should return parent reference: {parent_head}"
        print("   ✓ Correctly finds reference in parent directory")

        return True

    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        # Import built-in modules
        import traceback

        traceback.print_exc()
        return False
    finally:
        if test_dir and os.path.exists(test_dir):
            shutil.rmtree(test_dir, ignore_errors=True)


def test_scan_assets_metadata_structure():
    """Test that _scan_assets_in_directory adds reference to metadata."""
    print("Testing metadata structure with reference...")

    test_dir = None
    try:
        # Import local modules
        from cgame_avatar_factory.hair_studio.data.data_manager import _scan_assets_in_directory

        # Create minimal test structure
        test_dir = tempfile.mkdtemp(prefix="scan_test_")

        # Create asset with reference
        asset_dir = os.path.join(test_dir, "test_asset")
        os.makedirs(asset_dir)

        # Hair model
        with open(os.path.join(asset_dir, "test_asset.fbx"), "w") as f:
            f.write("# Hair")

        # Reference model
        with open(os.path.join(asset_dir, "test_asset_head.obj"), "w") as f:
            f.write("# Head")

        # Thumbnail
        with open(os.path.join(test_dir, "test_asset.jpg"), "w") as f:
            f.write("# Thumbnail")

        # Scan assets
        assets = _scan_assets_in_directory(test_dir, "card")

        assert len(assets) == 1, f"Expected 1 asset, got {len(assets)}"
        asset = assets[0]

        # Check metadata structure
        assert "metadata" in asset, "Asset should have metadata"
        metadata = asset["metadata"]
        assert "file_path" in metadata, "Metadata should have file_path"
        assert "reference" in metadata, "Metadata should have reference"

        # Verify reference path
        ref_path = metadata["reference"]
        assert os.path.isfile(ref_path), f"Reference file should exist: {ref_path}"
        assert "head" in os.path.basename(ref_path).lower(), "Reference should contain 'head'"

        print("   ✓ Metadata structure is correct")
        print("   ✓ Reference field is properly set")

        return True

    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        # Import built-in modules
        import traceback

        traceback.print_exc()
        return False
    finally:
        if test_dir and os.path.exists(test_dir):
            shutil.rmtree(test_dir, ignore_errors=True)


def test_hair_operations_reference_usage():
    """Test that hair_operations correctly uses reference from metadata."""
    print("Testing hair_operations reference usage...")

    try:
        # Use patch context manager to ensure proper cleanup
        with patch("cgame_avatar_factory.hair_studio.maya_api.hair_operations.WrapHairMesh") as mock_wrap_hair:
            # Import local modules
            import cgame_avatar_factory.hair_studio.maya_api.hair_operations as hair_ops

            # Create test asset data with reference
            test_asset_data = {
                "name": "Test Hair",
                "metadata": {
                    "file_path": "/mock/hair.fbx",
                    "reference": "/mock/head.obj",
                },
            }

            # Configure the mock
            mock_hair_mesh = MagicMock()
            mock_hair_mesh.hair_target = "mock_hair_node"
            mock_hair_mesh.hair_head_mesh = "mock_head_mesh_node"
            mock_wrap_hair.return_value = mock_hair_mesh

            result = hair_ops.create_hair_node(test_asset_data)

            # Should return success result
            assert isinstance(result, dict), "Should return dict result"
            assert "success" in result, "Should have success key"
            assert result["success"] is True, "Should return success=True"

            # Check new node_names structure
            assert "node_names" in result, "Should have node_names key"
            node_names = result["node_names"]
            assert isinstance(node_names, dict), "node_names should be a dict"
            assert "hair_asset" in node_names, "Should have hair_asset key"
            assert "ref_head" in node_names, "Should have ref_head key"
            assert node_names["hair_asset"] == "mock_hair_node", "hair_asset should match mock value"
            assert node_names["ref_head"] == "mock_head_mesh_node", "ref_head should match mock value"

            print("   ✓ hair_operations can process reference metadata with new node_names structure")
            return True

    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        # Import built-in modules
        import traceback

        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("=" * 50)
    print("Reference Asset Core Functionality Test")
    print("=" * 50)

    # Core tests - focused on the new functionality
    tests = [
        test_find_reference_asset_core_logic,
        test_scan_assets_metadata_structure,
        test_hair_operations_reference_usage,
    ]

    for test_func in tests:
        if not test_func():
            print(f"\n❌ Test {test_func.__name__} failed")
            return False
        print()

    print("=" * 50)
    print("🎉 All core reference tests passed!")
    print("✅ Reference field functionality working correctly")
    print("=" * 50)

    return True


if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
