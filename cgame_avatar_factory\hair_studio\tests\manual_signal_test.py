#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Manual Test for UI Signal Connection

Run this script and manually click buttons to see if events are handled by the manager.
"""

import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

print("=== Manual UI Signal Connection Test ===")
print("This test creates the UI and you can manually click buttons to see if events are handled.")
print("Look for log messages from GlobalDeformerUIEventManager in the console.")
print()

try:
    from qtpy import QtWidgets, QtCore
    
    # Import the main widget
    from cgame_avatar_factory.hair_studio.ui.card_hair_modify.global_deformer_widget import GlobalDeformerWidget
    
    class TestApp(QtWidgets.QApplication):
        def __init__(self, argv):
            super().__init__(argv)
            self.setup_test_window()
        
        def setup_test_window(self):
            # Create main window
            self.window = QtWidgets.QMainWindow()
            self.window.setWindowTitle("Manual UI Signal Test - Click buttons to test!")
            self.window.resize(1200, 800)
            
            # Create the deformer widget
            self.deformer_widget = GlobalDeformerWidget()
            
            # Create a container with instructions
            container = QtWidgets.QWidget()
            layout = QtWidgets.QVBoxLayout(container)
            
            # Add instructions
            instructions = QtWidgets.QLabel("""
<h3>Manual Test Instructions:</h3>
<p><b>1. 选择毛发对象:</b> Click "请选择毛发面片来生成曲线" button</p>
<p><b>2. 生成曲线:</b> Click "生成曲线>>" button</p>
<p><b>3. 选择Mesh对象:</b> Click "请选择头发mesh对象" button</p>
<p><b>4. 创建驱动:</b> Click "创建驱动>>" button</p>
<p><b>5. 添加驱动:</b> Click "冻结" button in middle panel</p>
<p><b>6. 删除驱动:</b> Select a driver and click "删除" button</p>
<p><b>Watch the console for log messages from GlobalDeformerUIEventManager!</b></p>
            """)
            instructions.setWordWrap(True)
            instructions.setStyleSheet("background-color: #f0f0f0; padding: 10px; margin: 10px;")
            layout.addWidget(instructions)
            
            # Add the deformer widget
            layout.addWidget(self.deformer_widget)
            
            self.window.setCentralWidget(container)
            self.window.show()
            
            print("✓ Test window created successfully!")
            print("✓ UI Event Manager initialized:", hasattr(self.deformer_widget, 'ui_event_manager'))
            print("✓ Now manually click buttons and watch console for event manager logs!")
    
    # Create and run the application
    app = TestApp(sys.argv)
    sys.exit(app.exec_())
    
except ImportError as e:
    print(f"✗ Import Error: {e}")
    print("Make sure all dependencies are installed and paths are correct.")
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()