# Import built-in modules
import os
import re
from typing import List

# Import third-party modules
from dna import BinaryStreamReader
from dna import BinaryStreamWriter
from dna import CoordinateSystem
from dna import DataLayer_All
from dna import DataLayer_Definition
from dna import DataLayer_Geometry
from dna import FileStream
from dna import Status
from dna_viewer import DNA
from dna_viewer import RigConfig
from dna_viewer import build_meshes
from dna_viewer.builder.mesh import Mesh
from maya import OpenMaya
import maya.api.OpenMaya as om

# Import Maya modules
import maya.cmds as cmds

# Import local modules
import cgame_avatar_factory.common.constants as const
import cgame_avatar_factory.common.utils.axis_align_utils as axis_align_utils
from cgame_avatar_factory.face_sculpting_center.utils import mesh_util


class TemplateDna:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(TemplateDna, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not hasattr(self, "_initialized"):
            self._initialized = False

        if self._initialized:
            return

        self._path = os.path.join(const.CONFIG_PATH, const.BASE_DNA_PATH)
        self._reader = None
        self._mesh_name_list = None
        self._load_data()
        self._initialized = True

    def _load_data(self):
        self._reader = load_dna_calib(self._path)
        self._mesh_name_list = get_mesh_name(self._path)

    @property
    def path(self):
        return self._path

    @property
    def reader(self):
        return self._reader

    @property
    def mesh_name_list(self):
        return self._mesh_name_list

    @classmethod
    def get_mesh_name(cls):
        return cls().mesh_name_list

    @classmethod
    def get_reader(cls):
        return cls().reader


def get_mesh_index_from_name(reader, mesh_name: str):
    """Get mesh index from name

    Args:
        mesh_name: Name of mesh

    Returns:
        int: Index of mesh
    """
    for index in range(reader.getMeshCount()):
        if reader.getMeshName(index) == mesh_name:
            return index
    return -1


def run_joints_command(reader, writer):
    """Update joint transforms in DNA file

    Gets current joint translations and rotations from Maya
    and updates them in the DNA writer.

    Args:
        writer: DNA writer object
        reader: DNA reader object
    """
    joint_translations = []
    joint_rotations = []
    for i in range(reader.getJointCount()):
        joint_name = reader.getJointName(i)
        all_bones = cmds.ls(type="joint")
        if joint_name in all_bones:
            translation = cmds.xform(joint_name, query=True, translation=True)
            rotation = cmds.joint(joint_name, query=True, orientation=True)
            joint_translations.append(translation)
            joint_rotations.append(rotation)
        else:
            joint_translations.append([0, 0, 0])
            joint_rotations.append([0, 0, 0])

    writer.setNeutralJointTranslations(joint_translations)
    writer.setNeutralJointRotations(joint_rotations)


def init_face_dna():
    dna_path = os.path.join(const.CONFIG_PATH, const.FACE_RIG_DNA)
    output_dna_path = mesh_util.create_workspace_dna_dir(const.FACE_OUTPUT_DNA_NAME)
    reader = load_dna_calib(dna_path)
    writer = init_writer_from_reader(output_dna_path, reader)
    run_joints_command(reader, writer)
    writer.write()
    return output_dna_path


def load_dna_calib(dna_file: str, layer=DataLayer_All):
    """Load DNA data from file

    Args:
        dna_file: Path to DNA file
        layer: Data layer to load

    Returns:
        BinaryStreamReader: DNA reader instance

    Raises:
        RuntimeError: If DNA loading fails
    """
    stream = FileStream(dna_file, FileStream.AccessMode_Read, FileStream.OpenMode_Binary)
    reader = BinaryStreamReader(stream, layer)
    reader.read()
    if not Status.isOk():
        status = Status.get()
        raise RuntimeError("Error loading DNA: {}".format(status.message))
    return reader


def init_writer_from_reader(path, reader=None):
    """Save DNA data to file

    Args:
        reader: DNA reader instance
        path: Save path

    Returns:
        BinaryStreamWriter: DNA writer instance
    """
    stream = FileStream(
        path,
        FileStream.AccessMode_Write,
        FileStream.OpenMode_Binary,
    )
    writer = BinaryStreamWriter(stream)
    if reader != None:
        writer.setFrom(reader)
    return writer


def build_base_mesh(dna: DNA, config=None):
    """Build base mesh

    Args:
        dna: DNA instance
        config: Mesh build_face configuration
    """
    build_meshes(dna=dna, config=config)


def import_all_skin_clusters(dna_path: str):
    """Import all skin clusters

    Args:
        dna_path: DNA file path
    """
    dna = DNA(dna_path)

    config = RigConfig(
        add_skin_cluster=True,
    )

    for mesh_index in range(len(TemplateDna.get_mesh_name())):
        mesh_builder = Mesh(
            config=config,
            dna=dna,
            mesh_index=mesh_index,
        )
        mesh_builder.add_skin_cluster()


def get_mesh_name(dna_path: str):
    """Get list of mesh names

    Args:
        dna_path: DNA file path

    Returns:
        list: List of mesh names
    """
    reader = load_dna_calib(dna_path, DataLayer_Definition)
    name_list = []
    for index in range(reader.getMeshCount()):
        name_list.append(reader.getMeshName(index))
    return name_list


def get_mesh_index(dna_path: str):
    """Get list of mesh indices

    Args:
        dna_path: DNA file path

    Returns:
        list: List of mesh indices
    """
    reader = load_dna_calib(dna_path, DataLayer_Definition)
    index_list = []
    for index in range(reader.getMeshCount()):
        index_list.append(index)
    return index_list


def write_mesh(output_path, mesh_names):
    """Write meshes to DNA file.

    Args:
        output_path (str): Path to output DNA file.
        mesh_names(List):List of meshes
    Returns:
        list: List of mesh names written, or empty list if any mesh missing.
    """
    meshes = []
    for mesh in mesh_names:
        if not cmds.objExists(mesh):
            cmds.warning(f"Mesh {mesh} does not exist!")
            return []
        meshes.append(mesh)
    DNAMeshWriter(
        lods=[0],
        meshs={0: meshes},
        output_path=output_path,
    )
    return meshes


def get_coord_system():
    """Get CoordinateSystem.
    Returns:
        CoordinateSystem.
    """
    up_axis = cmds.upAxis(query=True, axis=True)  # 返回 'x', 'y' 或 'z'
    # Set coordinate system
    coord_system = CoordinateSystem()
    coord_system.xAxis = 0  # X axis direction (usually 0=Right)
    if up_axis == "z":
        # Z up: x=0, y=4, z=2
        coord_system.yAxis = 4
        coord_system.zAxis = 2
    elif up_axis == "y":
        # Y up: x=0, y=2, z=4
        coord_system.yAxis = 2
        coord_system.zAxis = 4
    else:
        # 其他情况，默认使用Y up
        coord_system.yAxis = 2
        coord_system.zAxis = 4
    return coord_system


class DNAMeshWriter:
    """Writes mesh data to DNA file format.

    Handles writing mesh geometry, topology, and attributes to DNA file.
    """

    def __init__(self, lods: list, meshs: dict, output_path: str):
        """Initialize DNA writer.

        Args:
            lods (list): List of LOD levels.
            meshs (dict): Dictionary mapping LOD levels to mesh names.
            output_path (str): Output DNA file path.

        Returns:
            None
        """
        self.output_path = output_path
        self.meshs = meshs
        self.lods = lods
        self.writer = init_writer_from_reader(self.output_path)
        self.writer.setCoordinateSystem(get_coord_system())
        self.set_mesh_indices()
        self.write_all_meshes()
        self.writer.write()

    def write_all_meshes(self):
        """Write all meshes to DNA file.

        Returns:
            None
        """
        mesh_index = 0
        self.writer.setLODCount(len(self.lods))
        for lod, mesh_list in self.meshs.items():
            for mesh in mesh_list:
                self.write_mesh_to_dna(mesh, mesh_index)
                mesh_index += 1

    def write_mesh_to_dna(self, mesh_name: str, mesh_index: int):
        """Write mesh data to DNA file.

        Args:
            mesh_name (str): Name of mesh to write.
            mesh_index (int): Index of mesh in DNA file.

        Returns:
            None
        """
        self.set_mesh_name(mesh_index, mesh_name)
        self.get_vertex_layouts(mesh_name, mesh_index)
        self.set_vertex_positions(mesh_index, mesh_util.get_vertex_positions(mesh_name))
        self.set_vertex_normals(mesh_index, self.get_vertex_normals(mesh_name))
        self.set_vertex_uvs(mesh_index, self.get_vertex_uvs(mesh_name))
        self.get_face_layouts(mesh_index, mesh_name)

    def get_vertex_layouts(self, mesh_name: str, mesh_index: int):
        """Get vertex layouts for mesh.

        Args:
            mesh_name (str): Name of mesh.
            mesh_index (int): Index of mesh in DNA file.

        Returns:
            list: List of vertex layouts.
        """
        layout = []
        uv_count = cmds.polyEvaluate(mesh_name, uv=True)
        position_list = []
        for uv_id in range(uv_count):
            _layout = []
            vtx_list = cmds.polyListComponentConversion(
                f"{mesh_name}.map[{uv_id}]",
                fromUV=True,
                toVertex=True,
            )
            vtx_id = int(re.search(r"\[(\d+)\]", vtx_list[0]).group(1))
            _layout.append(vtx_id)
            _layout.append(uv_id)
            _layout.append(uv_id)
            layout.append(_layout)
            position_list.append(vtx_id)
        self.set_vertex_layouts(mesh_index, layout)
        return position_list

    def get_vertex_positions(self, mesh_name: str):
        """Get vertex positions for mesh.

        Args:
            mesh_name (str): Name of mesh.

        Returns:
            list: List of vertex positions.
        """
        vertices_position = []
        vertices = cmds.ls(f"{mesh_name}.vtx[*]", fl=True)
        for vertex in vertices:
            position = cmds.pointPosition(vertex, world=True)
            vertices_position.append([position[0], position[1], position[2]])
        return vertices_position

    def get_vertex_normals(self, mesh_name: str):
        """Get vertex normals for mesh.

        Args:
            mesh_name (str): Name of mesh.

        Returns:
            list: List of vertex normals.
        """
        normals = []
        vertex_count = cmds.polyEvaluate(mesh_name, vertex=True)
        for i in range(vertex_count):
            vertex_normals = cmds.polyNormalPerVertex(f"{mesh_name}.vtx[{i}]", query=True, xyz=True)
            if vertex_normals is None:
                cmds.warning(f"No normals found for vertex {i} of mesh {mesh_name}.")
                continue
            avg_normal = [sum(vertex_normals[i::3]) / (len(vertex_normals) / 3) for i in range(3)]
            normals.append(avg_normal)
        return normals

    def get_vertex_uvs(self, mesh_name: str):
        """Get vertex UVs for mesh.

        Args:
            mesh_name (str): Name of mesh.

        Returns:
            list: List of vertex UVs.
        """
        uvs = []
        uv_list = cmds.ls(f"{mesh_name}.map[*]", flatten=True)
        for uv in uv_list:
            uv_position = cmds.polyEditUV(uv, query=True)
            uvs.append([uv_position[0], uv_position[1]])
        return uvs

    def get_face_layouts(self, mesh_index: int, mesh_name: str):
        """Get face layouts for mesh.

        Args:
            mesh_index (int): Index of mesh in DNA file.
            mesh_name (str): Name of mesh.

        Returns:
            None
        """
        sel = OpenMaya.MSelectionList()
        sel.add(mesh_name)
        mesh_obj = OpenMaya.MObject()
        sel.getDependNode(0, mesh_obj)

        face_itr = OpenMaya.MItMeshPolygon(mesh_obj)

        while not face_itr.isDone():
            face_vertices = []

            for i in range(face_itr.polygonVertexCount()):
                uv_id = OpenMaya.MScriptUtil()
                uv_id.createFromInt(0)
                uv_id_ptr = uv_id.asIntPtr()
                face_itr.getUVIndex(i, uv_id_ptr)
                layout_idx = OpenMaya.MScriptUtil(uv_id_ptr).asInt()
                face_vertices.append(layout_idx)

            self.set_face_layouts(mesh_index, face_itr.index(), face_vertices)
            face_itr.next()

    def set_mesh_name(self, mesh_index: int, name: str):
        """Set mesh name in DNA file.

        Args:
            mesh_index (int): Index of mesh in DNA file.
            name (str): Name of mesh.

        Returns:
            None
        """
        self.writer.setMeshName(mesh_index, name)

    def set_mesh_indices(self):
        """Set mesh indices in DNA file.

        Returns:
            None
        """
        mesh_indices = {}
        current_index = 0
        for lod in sorted(self.meshs.keys()):
            mesh_indices[lod] = []
            for mesh in self.meshs[lod]:
                mesh_indices[lod].append(current_index)
                current_index += 1

        for lod in sorted(self.meshs.keys()):
            self.writer.setLODMeshMapping(lod, lod)
            self.writer.setMeshIndices(lod, mesh_indices[lod])

    def set_face_layouts(self, mesh_index: int, face_index: int, face_layouts: list):
        """Set face layouts in DNA file.

        Args:
            mesh_index (int): Index of mesh in DNA file.
            face_index (int): Index of face in mesh.
            face_layouts (list): List of face layouts.

        Returns:
            None
        """
        self.writer.setFaceVertexLayoutIndices(mesh_index, face_index, face_layouts)

    def set_joint_hierarchy(self, hierarchy_indices: list):
        """Set joint hierarchy in DNA file.

        Args:
            hierarchy_indices (list): List of joint hierarchy indices.

        Returns:
            None
        """
        self.writer.setJointHierarchy(hierarchy_indices)

    def set_vertex_layouts(self, mesh_index: int, layouts: list):
        """Set vertex layouts in DNA file.

        Args:
            mesh_index (int): Index of mesh in DNA file.
            layouts (list): List of vertex layouts.

        Returns:
            None
        """
        self.writer.setVertexLayouts(mesh_index, layouts)

    def set_vertex_positions(self, mesh_index: int, positions: list):
        """Set vertex positions in DNA file.

        Args:
            mesh_index (int): Index of mesh in DNA file.
            positions (list): List of vertex positions.

        Returns:
            None
        """
        self.writer.setVertexPositions(mesh_index, positions)

    def set_vertex_normals(self, mesh_index: int, normals: list):
        """Set vertex normals in DNA file.

        Args:
            mesh_index (int): Index of mesh in DNA file.
            normals (list): List of vertex normals.

        Returns:
            None
        """
        self.writer.setVertexNormals(mesh_index, normals)

    def set_vertex_uvs(self, mesh_index: int, uvs: list):
        """Set vertex UVs in DNA file.

        Args:
            mesh_index (int): Index of mesh in DNA file.
            uvs (list): List of vertex UVs.

        Returns:
            None
        """
        self.writer.setVertexTextureCoordinates(mesh_index, uvs)


class DNAMeshBuilder:
    def __init__(
        self,
        dna_path=None,
        layer=DataLayer_Geometry,
        _mesh_name=None,
        suffix=None,
        skip_uv=False,
        merge_uvs=True,
    ):
        self.reader = None
        self.dna_path = dna_path
        self._mesh_name = _mesh_name
        self.result = None
        self._suffix = suffix
        self.skip_uv = skip_uv
        self.merge_uvs = merge_uvs
        try:
            self.reader = load_dna_calib(self.dna_path, layer)
        except Exception:
            return

    def build_mesh(self, _mesh_name):
        if not self.reader:
            return None

        result = {}

        for lod_index in range(self.reader.getLODCount()):
            meshs = []
            for mesh_index in self.reader.getMeshIndicesForLOD(lod_index):
                mesh_name = self.reader.getMeshName(mesh_index)

                if _mesh_name and mesh_name != _mesh_name:
                    continue

                mesh_obj = self._create_mesh_with_naming(mesh_index, mesh_name)
                if _mesh_name:
                    return mesh_obj

                if mesh_obj:
                    meshs.append(mesh_obj)

            result[lod_index] = meshs
        return result

    def _build_meshes(self):
        if self.result is None:
            self.result = self.build_mesh(self._mesh_name)
        return self.result if self.result is not None else []

    def _create_mesh_with_naming(self, mesh_index, mesh_name):
        if cmds.objExists(mesh_name):
            cmds.rename(mesh_name, f"{mesh_name}_old")

        mesh_obj = self._build_mesh(mesh_index, mesh_name)
        if not mesh_obj:
            return None

        if self._suffix:
            mesh_obj = cmds.rename(mesh_obj, f"{mesh_obj}_{self._suffix}")

        if cmds.objExists(f"{mesh_name}_old"):
            cmds.rename(f"{mesh_name}_old", mesh_name)

        self._assign_lambert1_material(mesh_obj)

        return mesh_obj

    def _assign_lambert1_material(self, mesh_obj):
        try:
            if not cmds.objExists("lambert1"):
                cmds.shadingNode("lambert", asShader=True, name="lambert1")

            cmds.sets(mesh_obj, edit=True, forceElement="initialShadingGroup")
        except Exception:
            pass

    def _build_mesh(self, mesh_index, mesh_name):
        try:
            vertex_count = self.reader.getVertexPositionCount(mesh_index)
            dna_up_axis = get_dna_up_axis(self.dna_path)
            scene_up_axis = cmds.upAxis(q=True, axis=True).lower()
            vertices = []
            for v in range(vertex_count):
                pos = self.reader.getVertexPosition(mesh_index, v)

                if dna_up_axis != scene_up_axis:
                    pos = axis_align_utils.align_point_to_scene_up_axis(pos, dna_up_axis)
                vertices.append(pos)

            layout_count = self.reader.getVertexLayoutCount(mesh_index)

            vertex_layout_positions = []
            for layout_idx in range(layout_count):
                layout = self.reader.getVertexLayout(mesh_index, layout_idx)
                position_id = layout[0]
                vertex_layout_positions.append(position_id)

            face_count = self.reader.getFaceCount(mesh_index)

            face_vertex_layouts = []
            for f in range(face_count):
                face_layout_indices = self.reader.getFaceVertexLayoutIndices(mesh_index, f)
                face_vertex_layouts.append(list(face_layout_indices))

            polygon_faces, polygon_connects = self._convert_face_data(
                face_vertex_layouts,
                vertex_layout_positions,
            )

            if self.skip_uv:
                uv_data = None
            else:
                uv_data = self._get_uv_data(mesh_index, face_vertex_layouts, layout_count)

            mesh_obj = self._create_maya_mesh(mesh_name, vertices, polygon_faces, polygon_connects)

            if mesh_obj:
                if uv_data and not self.skip_uv:
                    self._add_uv_coordinates(mesh_obj, uv_data, polygon_faces)
                return mesh_obj
            else:
                return None

        except Exception as e:
            # Import built-in modules
            import traceback

            traceback.print_exc()
            return None

    def _convert_face_data(self, face_vertex_layouts, vertex_layout_positions):
        polygon_faces = []
        polygon_connects = []
        for vertices_layout_index_array in face_vertex_layouts:
            polygon_faces.append(len(vertices_layout_index_array))
            for vertex_layout_index in vertices_layout_index_array:
                if vertex_layout_index < len(vertex_layout_positions):
                    position_index = vertex_layout_positions[vertex_layout_index]
                    polygon_connects.append(position_index)

        return polygon_faces, polygon_connects

    def _get_uv_data(self, mesh_index, face_vertex_layouts, layout_count):
        try:
            uv_count = self.reader.getVertexTextureCoordinateCount(mesh_index)
            if uv_count == 0:
                return None

            texture_coordinates = []
            for uv_idx in range(uv_count):
                uv = self.reader.getVertexTextureCoordinate(mesh_index, uv_idx)
                texture_coordinates.append([uv[0], uv[1]])

            coordinate_indices = []
            for layout_idx in range(layout_count):
                layout = self.reader.getVertexLayout(mesh_index, layout_idx)
                texture_coordinate_id = layout[1]
                coordinate_indices.append(texture_coordinate_id)

            texture_coordinate_us = []
            texture_coordinate_vs = []
            texture_coordinate_indices = []

            index_counter = 0
            for vertices_layout_index_array in face_vertex_layouts:
                for vertex_layout_index in vertices_layout_index_array:
                    if vertex_layout_index < len(coordinate_indices):
                        tex_coord_idx = coordinate_indices[vertex_layout_index]
                        if tex_coord_idx < len(texture_coordinates):
                            texture_coordinate = texture_coordinates[tex_coord_idx]
                            texture_coordinate_us.append(texture_coordinate[0])
                            texture_coordinate_vs.append(texture_coordinate[1])
                            texture_coordinate_indices.append(index_counter)
                            index_counter += 1

            return {
                "us": texture_coordinate_us,
                "vs": texture_coordinate_vs,
                "indices": texture_coordinate_indices,
            }

        except Exception as e:
            return None

    def _merge_duplicate_uvs(self, uv_data, tolerance=1e-5):
        """优化的UV合并算法，使用空间哈希"""
        us = uv_data["us"]
        vs = uv_data["vs"]
        indices = uv_data["indices"]

        # 使用更大的网格大小以提高性能
        grid_size = int(1.0 / tolerance)
        uv_grid = {}
        merged_uvs = []
        uv_map = [0] * len(us)  # 预分配映射数组

        for i, (u, v) in enumerate(zip(us, vs)):
            # 量化UV坐标到网格
            grid_u = int(u * grid_size + 0.5)  # 使用int转换而非round
            grid_v = int(v * grid_size + 0.5)
            grid_key = (grid_u, grid_v)

            if grid_key in uv_grid:
                merged_index = uv_grid[grid_key]
            else:
                merged_index = len(merged_uvs)
                merged_uvs.append((u, v))
                uv_grid[grid_key] = merged_index

            uv_map[i] = merged_index

        # 快速构建结果索引
        merged_indices = [uv_map[idx] for idx in indices]

        if merged_uvs:
            merged_us, merged_vs = zip(*merged_uvs)
            return {
                "us": list(merged_us),
                "vs": list(merged_vs),
                "indices": merged_indices,
            }
        else:
            return {
                "us": [],
                "vs": [],
                "indices": [],
            }

    def _add_uv_coordinates(self, mesh_obj, uv_data, polygon_faces):
        """优化版本的UV坐标添加"""
        selection_list = om.MSelectionList()
        selection_list.add(mesh_obj)
        dag_path = selection_list.getDagPath(0)
        mesh_fn = om.MFnMesh(dag_path)

        # 根据设置决定是否合并重复UV
        if self.merge_uvs:
            merged_uv_data = self._merge_duplicate_uvs(uv_data)
        else:
            merged_uv_data = uv_data

        u_array = om.MFloatArray()
        v_array = om.MFloatArray()
        u_array.setLength(len(merged_uv_data["us"]))
        v_array.setLength(len(merged_uv_data["vs"]))

        for i, u in enumerate(merged_uv_data["us"]):
            u_array[i] = u
        for i, v in enumerate(merged_uv_data["vs"]):
            v_array[i] = v

        mesh_fn.setUVs(u_array, v_array)

        total_uv_indices = sum(polygon_faces)
        uv_counts = om.MIntArray()
        uv_ids = om.MIntArray()
        uv_counts.setLength(len(polygon_faces))
        uv_ids.setLength(total_uv_indices)

        face_idx = 0
        uv_idx = 0
        for face_count in polygon_faces:
            uv_counts[face_idx] = face_count
            face_idx += 1

            for _ in range(face_count):
                if uv_idx < len(merged_uv_data["indices"]):
                    uv_ids[uv_idx] = merged_uv_data["indices"][uv_idx]
                uv_idx += 1

        mesh_fn.assignUVs(uv_counts, uv_ids)

    def _create_maya_mesh(self, mesh_name, vertices, polygon_faces, polygon_connects):
        try:
            mesh_fn = om.MFnMesh()

            points = om.MPointArray()
            for vertex in vertices:
                points.append(om.MPoint(vertex[0], vertex[1], vertex[2]))

            face_counts = om.MIntArray()
            face_connects = om.MIntArray()

            for count in polygon_faces:
                face_counts.append(count)
            for connect in polygon_connects:
                face_connects.append(connect)

            mesh_obj = mesh_fn.create(points, face_counts, face_connects)

            dag_path = om.MDagPath.getAPathTo(mesh_obj)
            transform_fn = om.MFnTransform(dag_path.transform())
            transform_fn.setName(mesh_name)

            return dag_path.partialPathName()

        except Exception as e:
            # Import built-in modules
            import traceback

            traceback.print_exc()
            return None


def get_dna_up_axis(base_dna_path: str):
    """Setup DNA reader and determine coordinate system."""
    base_dna = DNA(base_dna_path)
    up_axis = "y" if base_dna.coordinate_system == (0, 2, 4) else "z"
    return up_axis


def get_mesh_data_from_dna(dna_path: List[str], import_axis: str, source_mesh_name: str = None):
    """Extract mesh vertex data from DNA files.

    Args:
        dna_path: List of DNA file paths
        import_axis: Axis to align imported data to. "y" or "z".

    Returns:
        list: List of mesh data dictionaries
    """
    all_mesh_data = []

    for path in dna_path:
        _mesh_data = {}
        reader = load_dna_calib(path, DataLayer_Geometry)
        dna_up_axis = get_dna_up_axis(path)
        for mesh_index in range(reader.getMeshCount()):
            mesh_name = reader.getMeshName(mesh_index)
            if source_mesh_name:
                if mesh_name != source_mesh_name:
                    continue

            vertex_index = reader.getVertexPositionCount(mesh_index)
            mesh_data = mesh_util.get_vertex_positions(reader, vertex_index, mesh_index, import_axis, dna_up_axis)
            _mesh_data[mesh_name] = mesh_data
        all_mesh_data.append(_mesh_data)
    return all_mesh_data
