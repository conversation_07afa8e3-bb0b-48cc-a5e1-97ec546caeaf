# Import standard modules
# Import built-in modules
import os

# Import third-party modules
from dna_viewer import DNA
from dna_viewer import RigConfig
import maya.cmds as cmds

# Import local modules
from cgame_avatar_factory.body_workshop import constants as body_const
from cgame_avatar_factory.body_workshop.build_body import build_body_process as body
from cgame_avatar_factory.common import constants as const
import cgame_avatar_factory.common.state.scene_state_manager as scene_state_mgr
import cgame_avatar_factory.common.utils.axis_align_utils as axis_align_utils
import cgame_avatar_factory.face_sculpting_center.accessibility.ref_image_plane_utils as ref_image_plane_utils
import cgame_avatar_factory.face_sculpting_center.build_face.material_create as mat
import cgame_avatar_factory.face_sculpting_center.mesh_align as mesh_align
import cgame_avatar_factory.face_sculpting_center.utils.dna_utils as dna
import cgame_avatar_factory.face_sculpting_center.utils.mesh_util as mesh


class SingleDNABuilder:
    """Builder for handling single DNA file operations"""

    def __init__(self, progress_callback=None, base_merge_page=None):
        """Initialize builder with optional progress callback and base merge page

        Args:
            progress_callback: Optional callback function for progress updates
            base_merge_page: Reference to the base merge page for accessing UI elements
        """
        self.progress_callback = progress_callback
        self.base_merge_page = base_merge_page

    def build(self, dna_file_path: str):
        """Build single DNA file

        Args:
            dna_file_path: Path to DNA file to process
        """
        if not os.path.exists(dna_file_path):
            raise FileNotFoundError(f"DNA file not found: {dna_file_path}")

        self._update_progress(10, "Preparing DNA file processing...")

        self._update_progress(30, "Loading DNA file...")

        source_dna_path = os.path.join(const.CONFIG_PATH, const.BASE_DNA_PATH)
        mesh_index_list = dna.get_mesh_index(source_dna_path)
        dna_obj = DNA(dna_file_path)

        self._update_progress(50, "Building model...")
        dna.build_base_mesh(dna_obj, config=RigConfig(meshes=mesh_index_list))

        # Check if build body checkbox is checked
        build_body = False
        if self.base_merge_page and hasattr(self.base_merge_page, "build_body_checkbox"):
            try:
                build_body = self.base_merge_page.build_body_checkbox.isChecked()

            except Exception:
                build_body = False

        if build_body:
            # TODO: Add body building logic here
            body_dna_path = os.path.join(const.CONFIG_PATH, body_const.BODY_DNA_PATH)
            body_name = body.get_body_type(dna_file_path)
            if body_name:
                body_dna_path = os.path.join(const.BODY_LIB_PATH, body_name, body_name + ".dna")

            body_mesh_builder = dna.DNAMeshBuilder(dna_path=body_dna_path, _mesh_name=body_const.BASE_BODY_MESH_NAME)
            body_mesh_builder._build_meshes()
            self._update_progress(60, "Building body...")

        self._update_progress(65, "Aligning axis...")
        imported_up_axis = "y" if dna_obj.coordinate_system == (0, 2, 4) else "z"
        axis_align_utils.align_object_to_scene_up_axis(
            const.HEAD_GRP,
            imported_up_axis,
        )

        self._update_progress(80, "Loading LODs...")
        mesh.load_lods_model()

        self._update_progress(90, "Applying materials...")
        mat.MaterialCreate(dna_path=dna_file_path)
        mesh.focus_camera_on_head()
        if build_body:
            mesh.assign_material(const.DX11_MATERIAL_NAME["body"], body_const.BASE_BODY_MESH_NAME)
            self._organize_body_mesh_hierarchy()
            weights_json_path = os.path.join(const.CONFIG_PATH, const.WEIGHT_JSON_PATH)
            mesh_align.align_mesh(body_dna_path, weights_json_path)

        self._update_progress(100, "DNA processing completed")
        scene_state_mgr.save()
        mesh.set_layer_display_type(const.HEAD_LAYER_NAME, 2)
        ref_image_plane_utils.reset_view_access_history()

    def _update_progress(self, value: int, message: str):
        """Update progress if callback is available

        Args:
            value: Progress value (0-100)
            message: Progress message
        """
        if self.progress_callback:
            self.progress_callback(value, message)

    def _organize_body_mesh_hierarchy(self):
        """组织身体网格层级结构

        获取geometry_grp节点，在其下创建body_lod0_grp组，并将body_lod0_mesh移动到该组中。
        """
        body_lod0_grp = body_const.BODY_LODO_GRP

        if not cmds.objExists(body_lod0_grp):
            cmds.group(empty=True, name=body_lod0_grp)
            cmds.parent(body_lod0_grp, const.GEOMETRY_GRP)

        cmds.parent(body_const.BASE_BODY_MESH_NAME, body_lod0_grp)
        mesh.create_layout([body_const.BASE_BODY_MESH_NAME], body_const.BODY_LAY)
        mesh.set_layer_unselectable(body_const.BODY_LAY, 2)
