"""Shader Configuration Module.

This module provides centralized shader configuration management for the Hair Studio.
It uses configurations defined in constants.py and provides a unified interface.

Configuration System Structure:
1. constants.py defines all basic configuration constants
2. This module provides configuration management and extension functionality
3. assign_shader.py accesses configurations through this module
"""

# Import built-in modules
import os
from typing import Any
from typing import Dict

# Import local modules
# Import constants for shader configurations
import cgame_avatar_factory.hair_studio.constants as const


class ShaderConfigRegistry:
    """Shader configuration registry with validation and extension support."""

    def __init__(self):
        self._configs = {}
        self._load_default_configs()

    def _load_default_configs(self):
        """Load default shader configurations from constants.py."""
        # Use SHADER_CONFIGS from constants.py
        self._configs.update(const.SHADER_CONFIGS)

    def register_config(self, key: str, config: Dict[str, Any]):
        """Register a new shader configuration.

        Args:
            key: Configuration key
            config: Shader configuration dictionary
        """
        self._validate_config(config)
        self._configs[key] = config

    def get_config(self, key: str) -> Dict[str, Any]:
        """Get shader configuration by key.

        Args:
            key: Configuration key

        Returns:
            Shader configuration dictionary
        """
        if key not in self._configs:
            raise KeyError(f"Unknown shader config: {key}")

        config = self._configs[key].copy()

        # Build full fx path if needed
        if config.get("fx_path") and not os.path.isabs(config["fx_path"]):
            config["fx_path"] = os.path.join(const.DEFAULT_SHADER_DIR, config["fx_path"])

        return config

    def list_configs(self) -> list:
        """List all available configuration keys."""
        return list(self._configs.keys())

    def _validate_config(self, config: Dict[str, Any]):
        """Validate shader configuration structure."""
        required_keys = ["shader_type", "texture_map", "params"]
        for key in required_keys:
            if key not in config:
                raise ValueError(f"Missing required key in shader config: {key}")


# ============================================================================
# Configuration Constants Reference - Use definitions from constants.py to avoid duplication
# ============================================================================

# Import configuration mapping from constants.py
ASSET_SHADER_MAPPING = const.SUB_ASSET_SHADER_MAP

# Parameter override configuration examples - Used to create shader variants
PARAMETER_OVERRIDES = {
    "eyebrow_fine": {
        "base_config": "dx11_hair",
        "overrides": {
            "Roughness": 0.8,
            "Scatter": 0.3,
        },
    },
    "beard_coarse": {
        "base_config": "dx11_hair",
        "overrides": {
            "Roughness": 0.9,
            "RootColor": (0.15, 0.10, 0.05),
            "TipColor": (0.20, 0.15, 0.10),
        },
    },
}

# Global registry instance - will be initialized after DEFAULT_SHADER_CONFIGS is defined
shader_registry = None


def _get_shader_registry():
    """Get or create the global shader registry."""
    global shader_registry
    if shader_registry is None:
        shader_registry = ShaderConfigRegistry()
    return shader_registry


def get_shader_config(key: str) -> Dict[str, Any]:
    """Get shader configuration by key.

    Args:
        key: Configuration key

    Returns:
        Shader configuration dictionary
    """
    return _get_shader_registry().get_config(key)


def register_shader_config(key: str, config: Dict[str, Any]):
    """Register a new shader configuration.

    Args:
        key: Configuration key
        config: Shader configuration dictionary
    """
    _get_shader_registry().register_config(key, config)


def create_config_with_overrides(base_key: str, overrides: Dict[str, Any]) -> Dict[str, Any]:
    """Create variant configuration based on existing configuration for special shader customization.

    Use Cases:
    1. Create fine eyebrow shader (based on dx11_hair, reduce roughness)
    2. Create coarse beard shader (based on dx11_hair, adjust color and scatter)
    3. Create transparent hair shader (based on dx11_hair, add transparency)
    4. Create metallic shader (based on blinn, adjust metallic properties)

    Args:
        base_key: Base configuration key name (e.g. "dx11_hair", "blinn")
        overrides: Parameter dictionary to override
            - "params": shader parameter overrides
            - "texture_map": texture mapping overrides
            - "fx_path": shader file path override

    Returns:
        New configuration dictionary with overrides applied

    Example:
        # Create fine eyebrow configuration
        fine_eyebrow_config = create_config_with_overrides("dx11_hair", {
            "params": {
                "Roughness": 0.8,      # Increase roughness
                "Scatter": 0.3,        # Reduce scatter
                "Brightness": 0.8      # Lower brightness
            }
        })

        # Create transparent hair configuration
        transparent_hair_config = create_config_with_overrides("dx11_hair", {
            "params": {
                "UseHairAlphaTexture": True,
                "transparency": 0.7     # Add transparency
            },
            "texture_map": {
                "HairAlphaTexture": "_alpha",  # Use different alpha texture suffix
            }
        })
    """
    base_config = get_shader_config(base_key)
    new_config = base_config.copy()

    # Deep copy nested dictionaries to avoid modifying original configuration
    new_config["params"] = base_config["params"].copy()
    new_config["texture_map"] = base_config["texture_map"].copy()

    # Apply parameter overrides
    if "params" in overrides:
        new_config["params"].update(overrides["params"])

    # Apply texture mapping overrides
    if "texture_map" in overrides:
        new_config["texture_map"].update(overrides["texture_map"])

    # Apply other overrides
    for key, value in overrides.items():
        if key not in ["params", "texture_map"]:
            new_config[key] = value

    return new_config


def get_preset_config(preset_key: str) -> Dict[str, Any]:
    """Get preset shader configuration variant.

    Use presets defined in PARAMETER_OVERRIDES to create special-purpose shader configurations.

    Args:
        preset_key: Preset key name (e.g. "eyebrow_fine", "beard_coarse")

    Returns:
        Preset configuration dictionary

    Raises:
        KeyError: If preset key does not exist

    Example:
        # Use preset to create fine eyebrow shader
        fine_config = get_preset_config("eyebrow_fine")
        register_shader_config("eyebrow_fine", fine_config)

        # Use in asset mapping
        const.SUB_ASSET_SHADER_MAP["fine_eyebrow"] = "eyebrow_fine"
    """
    if preset_key not in PARAMETER_OVERRIDES:
        available_presets = list(PARAMETER_OVERRIDES.keys())
        raise KeyError(f"Preset '{preset_key}' does not exist. Available presets: {available_presets}")

    preset = PARAMETER_OVERRIDES[preset_key]
    base_key = preset["base_config"]
    overrides = preset["overrides"]

    return create_config_with_overrides(base_key, overrides)
