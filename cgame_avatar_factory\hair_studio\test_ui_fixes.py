#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试UI修复和事件管理器功能
"""

import sys
import os
import logging

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_ui_event_manager():
    """测试UIEventManager功能"""
    print("=== 测试UIEventManager功能 ===")
    
    try:
        from cgame_avatar_factory.hair_studio.data.gwrap_data import GlobalDeformerManager
        from cgame_avatar_factory.hair_studio.manager.global_deformer_ui_event_manager import GlobalDeformerUIEventManager
        
        # 创建数据管理器
        data_manager = GlobalDeformerManager()
        print("✓ 数据管理器创建成功")
        
        # 创建UI事件管理器
        ui_event_manager = GlobalDeformerUIEventManager(data_manager)
        print("✓ UI事件管理器创建成功")
        
        # 测试选择对象事件
        hair_obj = ui_event_manager.handle_select_hair_object_event()
        print(f"✓ 选择毛发对象: {hair_obj}")
        
        mesh_obj = ui_event_manager.handle_select_mesh_object_event()
        print(f"✓ 选择mesh对象: {mesh_obj}")
        
        # 测试生成曲线事件
        curve_data = ui_event_manager.handle_generate_curve_event(hair_obj)
        print(f"✓ 生成曲线数据: {curve_data}")
        
        # 测试创建驱动事件
        success = ui_event_manager.handle_create_driver_event(curve_data, mesh_obj)
        print(f"✓ 创建驱动成功: {success}")
        
        # 检查数据管理器状态
        deformers = data_manager.get_all_deformers()
        print(f"✓ 当前变形器数量: {len(deformers)}")
        
        if deformers:
            print(f"✓ 第一个变形器: {deformers[0].name}")
        
        print("=== UIEventManager测试完成 ===\n")
        return True
        
    except Exception as e:
        print(f"✗ UIEventManager测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_left_panel_widget():
    """测试左侧面板组件"""
    print("=== 测试左侧面板组件 ===")
    
    try:
        # 尝试导入Qt相关模块
        from qtpy import QtWidgets, QtCore
        from cgame_avatar_factory.hair_studio.ui.card_hair_modify.components.left_panel_widget import LeftPanelWidget
        
        print("✓ 左侧面板组件导入成功")
        
        # 测试组件创建（不创建实际窗口）
        # 这里只测试类定义是否正确
        widget_class = LeftPanelWidget
        print(f"✓ 左侧面板组件类: {widget_class.__name__}")
        
        # 检查关键方法是否存在
        methods = ['on_select_hair_object', 'on_select_mesh_object', 'on_generate_curve', 'on_create_driver']
        for method in methods:
            if hasattr(widget_class, method):
                print(f"✓ 方法存在: {method}")
            else:
                print(f"✗ 方法缺失: {method}")
                return False
        
        print("=== 左侧面板组件测试完成 ===\n")
        return True
        
    except Exception as e:
        print(f"✗ 左侧面板组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_qtextedit_methods():
    """测试QTextEdit方法调用"""
    print("=== 测试QTextEdit方法调用 ===")
    
    try:
        from qtpy import QtWidgets
        
        # 创建一个QTextEdit来测试方法
        text_edit = QtWidgets.QTextEdit()
        text_edit.setPlainText("测试文本")
        
        # 测试toPlainText方法
        text = text_edit.toPlainText()
        print(f"✓ toPlainText()方法正常: '{text}'")
        
        # 确认text()方法不存在（应该报错）
        try:
            text_edit.text()
            print("✗ text()方法不应该存在于QTextEdit")
            return False
        except AttributeError:
            print("✓ 确认QTextEdit没有text()方法，使用toPlainText()是正确的")
        
        print("=== QTextEdit方法测试完成 ===\n")
        return True
        
    except Exception as e:
        print(f"✗ QTextEdit方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试UI修复和事件管理器功能...\n")
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行测试
    tests = [
        ("QTextEdit方法调用", test_qtextedit_methods),
        ("UIEventManager功能", test_ui_event_manager),
        ("左侧面板组件", test_left_panel_widget),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"运行测试: {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    # 输出测试结果
    print("=" * 50)
    print("测试结果汇总:")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 所有测试通过！")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)