"""Middle Panel Widget Module.

This module provides the middle panel widget for curve driver list management.
"""

import logging
from typing import Optional

# Import Qt modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import dayu widgets
from dayu_widgets import MLabel, MPushButton

# Import local modules
from cgame_avatar_factory.hair_studio.constants import DEFAULT_MARGIN, DEFAULT_SPACING
from cgame_avatar_factory.hair_studio.data.gwrap_data import GlobalDeformer


class MiddlePanelWidget(QtWidgets.QWidget):
    """中间面板组件：曲线驱动列表管理"""
    
    # 定义信号
    add_deformer_requested = QtCore.Signal()  # 请求添加变形器
    remove_deformer_requested = QtCore.Signal()  # 请求删除变形器
    deformer_selection_changed = QtCore.Signal(object, object)  # 选择变化 (current, previous)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._logger = logging.getLogger(__name__)
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """设置UI布局"""
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setSpacing(DEFAULT_SPACING)
        main_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)
        
        # 创建曲线驱动组
        drivers_group = self.create_curve_drivers_section()
        main_layout.addWidget(drivers_group)
    
    def create_curve_drivers_section(self):
        """创建曲线驱动区域"""
        drivers_group = QtWidgets.QGroupBox("曲线驱动 Curve Drivers")
        drivers_layout = QtWidgets.QVBoxLayout(drivers_group)
        drivers_layout.setSpacing(DEFAULT_SPACING)
        drivers_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)
        
        # Drivers list
        self.drivers_list = QtWidgets.QListWidget()
        self.drivers_list.setMinimumHeight(200)  # 设置最小高度
        drivers_layout.addWidget(self.drivers_list, 1)  # 拉伸因子为1，占据大部分空间
        
        # Buttons layout
        buttons_layout = QtWidgets.QHBoxLayout()
        
        # Add driver button
        self.freeze_btn = MPushButton("冻结")
        buttons_layout.addWidget(self.freeze_btn)
        
        # Remove driver button
        self.remove_driver_btn = MPushButton("删除")
        buttons_layout.addWidget(self.remove_driver_btn)
        
        # Add stretch to push buttons to left
        buttons_layout.addStretch()
        
        drivers_layout.addLayout(buttons_layout)
        
        return drivers_group
    
    def setup_connections(self):
        """设置信号连接"""
        self.freeze_btn.clicked.connect(self.on_add_driver)
        self.remove_driver_btn.clicked.connect(self.on_remove_driver)
        self.drivers_list.currentItemChanged.connect(self.on_selection_changed)
    
    def on_add_driver(self):
        """处理添加驱动按钮点击"""
        print(f"[EVENT] Add Driver button clicked")
        self.add_deformer_requested.emit()
    
    def on_remove_driver(self):
        """处理删除驱动按钮点击"""
        print(f"[EVENT] Remove Driver button clicked")
        
        current_item = self.drivers_list.currentItem()
        if not current_item:
            print(f"[EVENT] Warning: No driver selected for removal")
            self._logger.warning("请先选择要删除的变形器")
            return
        
        self.remove_deformer_requested.emit()
    
    def on_selection_changed(self, current, previous=None):
        """处理选择变化"""
        _ = previous  # Unused parameter
        print(f"[EVENT] Driver selection changed")
        self.deformer_selection_changed.emit(current, previous)
    
    def add_deformer_to_list(self, deformer: GlobalDeformer):
        """添加变形器到列表"""
        print(f"[UI] Adding deformer to list: {deformer.name}")
        self.drivers_list.addItem(deformer.name)
        print(f"[UI] Total items in list: {self.drivers_list.count()}")
    
    def remove_deformer_from_list(self, deformer_name: str):
        """从列表中删除变形器"""
        print(f"[UI] Removing deformer from list: {deformer_name}")
        
        for i in range(self.drivers_list.count()):
            item = self.drivers_list.item(i)
            if item and item.text() == deformer_name:
                self.drivers_list.takeItem(i)
                print(f"[UI] Removed from list, remaining items: {self.drivers_list.count()}")
                break
    
    def clear_selection(self):
        """清除选择"""
        print(f"[UI] Clearing list selection")
        self.drivers_list.clearSelection()
    
    def select_deformer_by_name(self, deformer_name: str):
        """根据名称选择变形器"""
        print(f"[UI] Selecting deformer by name: {deformer_name}")
        
        for i in range(self.drivers_list.count()):
            item = self.drivers_list.item(i)
            if item and item.text() == deformer_name:
                self.drivers_list.setCurrentRow(i)
                print(f"[UI] Selected deformer at row: {i}")
                break
    
    def get_current_deformer_name(self) -> Optional[str]:
        """获取当前选中的变形器名称"""
        current_item = self.drivers_list.currentItem()
        if current_item:
            return current_item.text()
        return None
    
    def get_deformer_count(self) -> int:
        """获取变形器数量"""
        return self.drivers_list.count()
