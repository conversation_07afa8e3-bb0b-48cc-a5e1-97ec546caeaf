
import maya.cmds as cmds
from functools import partial
import maya.mel as mel

MENU_PREFIX = "[GWrap变形器] "

# 默认菜单配置示例
DEFAULT_MENU_CONFIG = [
    {
        "label": MENU_PREFIX + "生成曲线",
        "selection_type": "mesh",  # 选择类型：transform, mesh, nurbsCurve等
        "component_type": None,  # 组件类型：face, vertex, edge 或 None
        "selected_obj_num": 1,
        "command": lambda: create_curve_from_sels()
    },
    {
        "label": MENU_PREFIX + "生成变形器",
        "selection_type": "nurbsCurve", 
        "component_type": None,
        "selected_obj_num": 2,
        "command": lambda: create_deformer_from_curve()
    },
    {
        "label": MENU_PREFIX + "调整曲线受影响面",
        "selection_type": "nurbsCurve",  # 选择类型：transform, mesh, nurbsCurve等
        "component_type": None,  # 组件类型：face, vertex, edge 或 None
        "selected_obj_num": 1,
        "command": lambda: adjust_curve_deformer_weight()
    },
]

def adjust_curve_deformer_weight():
    print("---------------adjust_curve_deformer_weight-----------------------")
    
    sel_curve = cmds.ls(sl=1, long=1)[0]
    # if cv convert to mesh
    sel_curve = sel_curve.split('.')[0]
    print("sel_curve = {}".format(sel_curve))

    # checkout output wire deformer
    shape_node = cmds.listRelatives(sel_curve, shapes=True, f=True)[0]
    print("shape_node = {}".format(shape_node))
    des_nodes = cmds.listConnections(shape_node, d=1)
    print("des_nodes = {}".format(des_nodes))
    deformer = None
    for node in des_nodes:
        if cmds.objectType(node) == "wire":
            deformer = node
            break
    if not deformer:
        cmds.warning("No wire deformer found for curve: {}".format(sel_curve))
        return
    # find deformer down node
    deformer_mesh = cmds.listConnections("{0}.outputGeometry".format(deformer), d=1)[0]
    print("deformer_mesh = {}".format(deformer_mesh))

    cmds.inViewMessage(amg="请选择受影响面，并按下中间的\"结束选择\"键结束", pos='bottomCenter', fade=True)   

    if cmds.headsUpDisplay('HUDHelloButton', exists=True):
        cmds.headsUpDisplay('HUDHelloButton', remove=True)
    
    def end_select(deformer, deformer_mesh, *args):
        print("end_select")
        sels = cmds.ls(sl=1, long=1)
        print("sels = {}".format(sels))
        # split other face
        filteredFaceList = [f for f in sels if deformer_mesh in f.split('.')[0]]
        print("filteredFaceList = {}".format(filteredFaceList))
        
        vert_num = cmds.polyEvaluate(deformer_mesh, vertex=True)
        print("vert_num = {}".format(vert_num))
        cmds.percent(deformer, "{}.vtx[0:{}]".format(deformer_mesh, vert_num-1), v=0)
        print("wire_deformer = {}".format(deformer))
        try:
            verts = cmds.polyListComponentConversion(filteredFaceList, toVertex=True)
            print("verts = {}".format(verts))
            cmds.percent(deformer, verts, v=1)
        except Exception as e:
            print("Error: {}, traceback = {}".format(e, traceback.format_exc()))
        finally:
            # finally need to remove HUD any way
            cmds.headsUpDisplay('HUDHelloButton', remove=True)
        
    cmds.hudButton('HUDHelloButton', s=7, b=7, vis=1, l='结束选择', bw=80, bsh='roundRectangle', rc=partial(end_select, deformer, deformer_mesh) )
    cmds.select(clear=True)
    mel.eval(
        """
        doMenuComponentSelection("{0}", "meshUVShell");
        """.format(deformer_mesh))

def create_curve_from_sels():
    print("---------------create_curve_from_sels-----------------------")
    
    cmds.inViewMessage(amg="create_curve_from_sels: 输入对象数 = {}".format(""), pos='topCenter', fade=True)
    # 0. selected face to shell ?

    # 1. convert select to edge perimeter
    _convert_sel_to_edge_perimeter()
    # 2. polygon to curve
    mesh_curve = _poly_to_curve()

    # 3. average curve
    average_curve = _average_all_curve(mesh_curve, "{}_average".format(mesh_curve))
    cmds.hide(average_curve)

    # # offset curve
    # offset_curve = _offset_curve(average_curve, "{}_offset".format(average_curve))
    # cmds.hide(offset_curve)

    # # 4. recalculate curve
    # generated_curve = _rebuild_curve(offset_curve, "{}_rebuild".format(offset_curve))
    generated_curve, rebuild_attr, offset_attr = combine_offset_rebuild_curve(average_curve, "{}_rebuild_offset".format(average_curve))

    # 5. set curve show way
    _set_curve_color(generated_curve)
    create_curve_params(rebuild_attr, offset_attr)
    
    # hub_ui = create_curve_param(average_curve, generated_curve)

    return generated_curve

DEFAULT_DISTANCE  = -0.8
DEFAULT_SPANS = 7

def combine_offset_rebuild_curve(src_curve, output_curve_name=None):
    
    # 1. offset
    offsets = cmds.offsetCurve(src_curve, ch=1, rn=0, cb =2, st=True, cl=True, cr=0, d=DEFAULT_DISTANCE, tol=0.01, sd=5, ugn=False)
    print(offsets)
    if not offsets or not len(offsets) == 2:
        cmds.warning("offset curve failed")
        return  
    offset_curve = offsets[0]
    offset_node = offsets[1]

    # 2.  rebuild
    rebuilds = cmds.rebuildCurve(offset_curve, ch=0, rpo=1, rt=0, end=1, kr=0, kcp=0, kep=1, kt=1, s=20, d=3, tol=0.01)
    print(rebuilds)
    if not rebuilds or not len(rebuilds) == 2:
        cmds.warning("rebuild curve failed")
        return
    rebuild_curve = rebuilds[0]
    rebuild_node = rebuilds[1]
    if output_curve_name and output_curve_name != rebuild_curve and not cmds.objExists(output_curve_name):
        cmds.rename(rebuild_curve, output_curve_name)
        rebuild_curve = output_curve_name
        print("rebuild curve renamed to {}".format(rebuild_curve))
    
    # attr
    cmds.setAttr(rebuild_node + ".spans", DEFAULT_SPANS)
    # cmds.setAttr(offset_node + ".distance", ui_distance)
    
    return rebuild_curve, "{0}.{1}".format(rebuild_node, "spans"), "{0}.{1}".format(offset_node, "distance")

def create_curve_params(point_num_attr, offset_attr):
    print("create_curve_param")
    cmds.inViewMessage(amg="拖动滑块重新调整曲线顶点数量", pos='bottomCenter', fade=True)

    curve_ui_name = 'curve_points'
    offset_ui_name = 'offset_distance'
    
    def on_drag(ui_name, point_num_attr, *args):
        print("on_drag, will rebuild curve for {0}, attr = {1}".format(ui_name, point_num_attr))
        value = cmds.hudSlider(ui_name, query=True, v=True)
        print("value = {}".format(value))
        cmds.setAttr(point_num_attr, value)
    

    # check if ui exists
    if curve_ui_name in cmds.headsUpDisplay(listHeadsUpDisplays=True):
        cmds.headsUpDisplay(curve_ui_name, remove=True)
    if offset_ui_name in cmds.headsUpDisplay(listHeadsUpDisplays=True):
        cmds.headsUpDisplay(offset_ui_name, remove=True)
    
    cmds.hudSlider(curve_ui_name,
        s=5,             # section位置
        b=5,             # block位置
        vis=True,
        label='curve points:',
        sl = 100,
        labelWidth=80,
        valueWidth=50,
        type='int',
        min=4, max=15,
        value=DEFAULT_SPANS,
        si=1,
        dragCommand=partial(on_drag, curve_ui_name, point_num_attr),
        # bl='确认', bw=30, bsh='roundRectangle', brc=partial(on_button, curve_ui_name, point_num_attr),
    )
    
    cmds.hudSlider(offset_ui_name,
        s=5,             # section位置
        b=4,             # block位置
        vis=True,
        label='offset distance:',
        sl = 100,
        labelWidth=80,
        valueWidth=50,
        type='float',
        min=-1.0, max=1.0,
        value=DEFAULT_DISTANCE,
        si=0.1,
        dragCommand=partial(on_drag, offset_ui_name, offset_attr),
    )
    def hideHud(ui_name, *args):
        if cmds.headsUpDisplay(ui_name, exists=True):
            cmds.headsUpDisplay(ui_name, remove=True)

    jobId = cmds.scriptJob(event=['SelectionChanged', partial(hideHud, curve_ui_name)], runOnce=True)
    # TODO: for safty, finally remove jobid

    def hideHud_offset(*args):
        if cmds.headsUpDisplay(offset_ui_name, exists=True):
            cmds.headsUpDisplay(offset_ui_name, remove=True)

    jobId_offset = cmds.scriptJob(event=['SelectionChanged', hideHud_offset], runOnce=True)
    # TODO: for safty, finally remove jobid

    return curve_ui_name


def create_deformer_from_curve():
    print("create_deformer_from_curve")
    # 读取选择，要求必须一个curve、一个mesh（mesh也可以是组件如face/edge/vertex）

    sels, sel_count = resolve_selection_type(True)

    cmds.inViewMessage(amg="create_deformer_from_curve: 输入对象数 = {}".format(sel_count), pos='topCenter', fade=True)
    if sel_count < 2:
        cmds.inViewMessage(amg="请选择一个曲线和一个模型", pos='topCenter', fade=True)
        return

    curve_node = None
    mesh_node = None
    is_component_sel = False  # 是否从组件选择中解析出的mesh
    mesh_faces = []  # 记录选中的面（展开后的组件列表）

    def base_object_path(sel_obj):
        """若是组件选择，返回其对象路径，否则返回自身。"""
        return sel_obj.split('.', 1)[0] if '.' in sel_obj else sel_obj

    # 在选择中查找第一个曲线与第一个网格
    for sel_obj, obj_type, component_type in sels:
        # 找曲线
        if curve_node is None and obj_type == 'nurbsCurve':
            curve_node = base_object_path(sel_obj)
            continue
        # 找网格（mesh 或其组件）
        if mesh_node is None and obj_type == 'mesh':
            mesh_node = base_object_path(sel_obj)
            if component_type is not None:
                is_component_sel = True
                # 如果是面组件，展开并记录
                if component_type == 'face':
                    try:
                        expanded = cmds.filterExpand(sel_obj, selectionMask=34) or []  # 34: faces
                        mesh_faces.extend(expanded)
                    except Exception:
                        # 兜底：直接记录原始组件
                        mesh_faces.append(sel_obj)
        elif obj_type == 'mesh' and component_type == 'face':
            # 另一种情况：先遇到曲线，再遇到面的情况下，mesh_node 已存在或未设置
            base = base_object_path(sel_obj)
            if mesh_node is None:
                mesh_node = base
            if base_object_path(sel_obj) == mesh_node:
                is_component_sel = True
                try:
                    expanded = cmds.filterExpand(sel_obj, selectionMask=34) or []
                    mesh_faces.extend(expanded)
                except Exception:
                    mesh_faces.append(sel_obj)

    # 校验必须各找到一个
    if not curve_node or not mesh_node:
        cmds.inViewMessage(amg="需要选择一个曲线与一个模型", pos='topCenter', fade=True)
        return

    # 若选择超过两个，这里也可以弹出提示或仅取第一对
    # 这里选择仅取第一对
    # 去重整理面列表
    if mesh_faces:
        mesh_faces = sorted(set(mesh_faces))

    print("Resolved selection -> curve: {}, mesh: {}, via_component: {}, faces: {}".format(
        curve_node, mesh_node, is_component_sel, len(mesh_faces)))
    print(mesh_faces)

    # TODO: 后续在此绑定/创建变形器的逻辑
    # e.g. bind curve and mesh
    wire_deformer = cmds.wire(mesh_node, w=curve_node, gw=False, en=1.0, ce=0.0, li=0.0)
    if not wire_deformer:
        cmds.inViewMessage(amg="创建变形器失败", pos='topCenter', fade=True)
        return
    wire_deformer =  wire_deformer[0]

    cmds.setAttr(wire_deformer + ".dropoffDistance[0]", 10)

    cmds.inViewMessage(amg="已经创建了wire deformer", pos='topCenter', fade=True)

    # TODO:set wire deformer weight for face
    # convert face to vertex and set weight
    if mesh_faces:
        
        vert_num = cmds.polyEvaluate(mesh_node, vertex=True)
        print("vert_num = {}".format(vert_num))
        cmds.percent(wire_deformer, "{}.vtx[0:{}]".format(mesh_node, vert_num-1), v=0)
        print("wire_deformer = {}".format(wire_deformer))
        verts = cmds.polyListComponentConversion(mesh_faces, toVertex=True)
        print("verts = {}".format(verts))
        cmds.percent(wire_deformer, verts, v=1)

    # make curve points editable, select curve cv control
    mel.eval(
        """
        doMenuNURBComponentSelection("{0}", "controlVertex");
        select -r {0}.cv[0] ;
        """.format(curve_node)
    )

def _convert_sel_to_edge_perimeter():

    from maya import mel
    sels = cmds.ls(sl=1, long=1)
    print("convert_sel_to_edge_perimeter, sels = {}".format(sels))

    mel.eval("ConvertSelectionToEdgePerimeter;")
    sel_edge = cmds.ls(sl=1,long=1)
    print("convert_sel_to_edge_perimeter, sel_edge = {}".format(sel_edge))

def _poly_to_curve():

    cmds.inViewMessage(amg="poly_to_curve", pos='topCenter', fade=True)
    bound_curve, poly2curve_node = cmds.polyToCurve(form =3, degree = 3, conformToSmoothMeshPreview = 1)
    print("bound_curve = {}, poly2curve_node = {}".format(bound_curve, poly2curve_node))
    return bound_curve

def _average_all_curve(closed_curve_name, output_name = "curve_average"):
    import math

    # TODO: handle curve node is not in the tip or tail position
    def get_curve_cvs_points(curve_name):
        """
        获取曲线所有CV点坐标，返回[(x,y,z), ...]列表
        """
        cvs = cmds.ls(curve_name + ".cv[*]", fl=True)
        points = [cmds.pointPosition(cv, w=True) for cv in cvs]
        return [tuple(p) for p in points]

    def create_curve_from_points(points, name="midLineCurve"):
        degree = 3 if len(points) >= 4 else 1
        curve = cmds.curve(p=points, degree=degree, name=name)
        return curve

    def generate_midline_curve_from_closed_long_curve(closed_curve_name, output_curve_name="midLineCurve"):
        points = get_curve_cvs_points(closed_curve_name)
        cmds.delete(closed_curve_name)
        num_points = len(points)
        
        if num_points % 2 != 0:
            cmds.warning("曲线CV点数不是偶数，无法均分为两条长边，已自动减去一个点")
            num_points -= 1
        
        half = num_points // 2
        
        edge1 = points[:half]
        edge2 = points[half:]
        def dist(a, b):
            return math.sqrt((a[0]-b[0])**2 + (a[1]-b[1])**2 + (a[2]-b[2])**2)
        dist_start = dist(edge1[0], edge2[0])
        dist_end = dist(edge1[0], edge2[-1])
        if dist_end < dist_start:
            edge2 = list(reversed(edge2))
        mid_points = [((a[0]+b[0]) / 2.0, (a[1]+b[1]) / 2.0, (a[2]+b[2]) / 2.0) for a, b in zip(edge1, edge2)]
        
        new_curve = create_curve_from_points(mid_points, output_curve_name)
        print("生成中线曲线：", new_curve)
        return new_curve

    return generate_midline_curve_from_closed_long_curve(closed_curve_name, output_name)

def _rebuild_curve(curve_name, rebuild_curve_name = "rebuild_curve", points_num = 10):
    print("rebuild curve: ", curve_name)
    if cmds.objExists(rebuild_curve_name):
        cmds.delete(rebuild_curve_name)
        print("delete rebuild curve: {}".format(rebuild_curve_name))
    new_curve = cmds.duplicate(curve_name, name=rebuild_curve_name)[0]
    print("new_curve = {}".format(new_curve))
    cmds.setAttr(new_curve + ".visibility", 1)
    if new_curve!= rebuild_curve_name:
        cmds.rename(new_curve, rebuild_curve_name)
    
    cmds.rebuildCurve(rebuild_curve_name, ch=1, rpo=1, rt=0, end=1, kr=0, kcp=0, kep=1, kt=1, s=points_num, d=3, tol=0.01)

    _set_curve_color(rebuild_curve_name)
    return rebuild_curve_name

def _offset_curve(curve_name, offset_curve_name = "offset_curve", offset_distance = 0.1):   
    print("offset curve: ", curve_name)
    if cmds.objExists(offset_curve_name):
        cmds.delete(offset_curve_name)
        print("delete offset curve: {}".format(offset_curve_name))
    new_curve = cmds.duplicate(curve_name, name=offset_curve_name)[0]
    print("new_curve = {}".format(new_curve))
    cmds.setAttr(new_curve + ".visibility", 1)
    if new_curve!= offset_curve_name:
        cmds.rename(new_curve, offset_curve_name)
    
    cmds.offsetCurve(offset_curve_name, ch=1, rn=0, cb =2, st=True, cl=True, cr=0, d=1, tol=0.01, sd=5, ugn=False)

    return offset_curve_name

def _set_curve_color(curve_name):
    
    shape_node = cmds.listRelatives(curve_name, shapes=True, f=True)[0]
    cmds.setAttr(shape_node + ".lineWidth", 5)
    cmds.setAttr(shape_node + ".alwaysDrawOnTop", 1)
    cmds.setAttr(shape_node + ".overrideEnabled", 1)
    # Enable RGB overrides
    cmds.setAttr(shape_node + ".overrideRGBColors", 1)

    cmds.setAttr(shape_node + ".overrideColorRGB", 1, 1, 0)

    cmds.setAttr(shape_node + ".dispCV", 1)
    cmds.setAttr(shape_node + ".dispHull", 1)

def resolve_selection_type(return_all=False):
    """
    获取当前选择的详细信息。

    参数:
        return_all (bool):
            - False(默认): 返回第一个选中项的三元组 (sel_obj, obj_type, component_type)
            - True: 返回列表 [ (sel_obj, obj_type, component_type), ... ]，包含所有选中项

    返回值:
        - 当 return_all=False 时: ((sel_obj, obj_type, component_type), 1)
          若无选择: ((None, None, None), 0)
        - 当 return_all=True 时: ([ (sel_obj, obj_type, component_type), ... ], selected_count)
          其中 selected_count 计算规则：
            * 若选择为 mesh 组件（face/vertex/edge），按“所属mesh去重”的数量计数
              例如: obj1.f[0], obj1.f[1] -> 计数为 1；obj1.f[0], obj2.f[1] -> 计数为 2
            * 其他资产类型（如 transform、mesh 整体、nurbsCurve 等），每个选中项计数 +1

    兼容性:
        请注意：函数现在总是会同时返回“选中数量”。原先仅解包3个返回值的调用需要同步调整。
    """
    selection = cmds.ls(selection=True, long=True) or []

    def inspect_one(sel_obj):
        component_type = None
        obj_type = None

        # 组件选择（面、点、边）
        if '.' in sel_obj:
            obj_path, component_part = sel_obj.split('.', 1)
            if component_part.startswith('f['):
                component_type = 'face'
            elif component_part.startswith('vtx['):
                component_type = 'vertex'
            elif component_part.startswith('e['):
                component_type = 'edge'

            shapes = cmds.listRelatives(obj_path, shapes=True, fullPath=True) or []
            if shapes:
                obj_type = cmds.objectType(shapes[0])
            else:
                obj_type = cmds.objectType(obj_path)
        else:
            # 普通对象
            obj_type = cmds.objectType(sel_obj)
            shapes = cmds.listRelatives(sel_obj, shapes=True, fullPath=True) or []
            if shapes:
                obj_type = cmds.objectType(shapes[0])

        return sel_obj, obj_type, component_type

    def base_object_path(sel_obj):
        """返回基础物体长路径（若为组件则去掉 . 后缀）。"""
        return sel_obj.split('.', 1)[0] if '.' in sel_obj else sel_obj

    # 全量解析
    parsed = [inspect_one(s) for s in selection]

    # 计算“选中物体数量”
    # 规则：
    # - mesh 组件（face/vertex/edge）：按所属 mesh 去重计数
    # - 其他资产：每个选中项 +1
    comp_types = {'face', 'vertex', 'edge'}
    component_mesh_set = set()
    non_component_count = 0

    for sel_obj, obj_type, component_type in parsed:
        if component_type in comp_types:
            component_mesh_set.add(base_object_path(sel_obj))
        else:
            # 非组件，直接按项计数
            non_component_count += 1

    total_selected_objects = len(component_mesh_set) + non_component_count

    if return_all:
        return parsed, total_selected_objects

    # 兼容旧逻辑: 无选择返回 (None, None, None)，否则仅返回第一个
    if not selection:
        return (None, None, None), 0
    first = parsed[0]
    # return_all=False 时，按需求固定返回 1
    return first, 1

class AllViewportObjectMenuManager:
    """
    给所有打开的 modelPanel 绑定右键菜单回调，
    根据配置信息动态添加自定义菜单项。
    支持绑定和解绑，完善的菜单项生命周期管理。
    """

    CUSTOM_MENU_TAG = "MY_CUSTOM_MENU_ITEM"
    
    def __init__(self, menu_config=None):
        # 记录已绑定的菜单名列表，方便解绑
        self.bound_menus = set()
        # 记录已创建的菜单项，用于生命周期管理 {menu_name: [item1, item2, ...]}
        self.created_menu_items = {}
        # 保存原始的postMenuCommand，用于恢复 {menu_name: original_command}
        self.original_menu_commands = {}
        # 菜单配置信息
        self.menu_config = menu_config or DEFAULT_MENU_CONFIG
        
        print("[初始化] 菜单管理器已创建，配置了 {} 个菜单项".format(len(self.menu_config)))

    def clear_custom_menu_items(self, menuName):
        """清理指定菜单的自定义菜单项"""
        if menuName in self.created_menu_items:
            for item in self.created_menu_items[menuName]:
                try:
                    if cmds.menuItem(item, query=True, exists=True):
                        print("[清理] 删除菜单项: {}".format(item))
                        cmds.deleteUI(item, menuItem=True)
                except Exception as e:
                    print("[清理] 删除菜单项失败: {}, 错误: {}".format(item, e))
            
            # 清空记录
            self.created_menu_items[menuName] = []

    def _enhanced_menu_callback(self, menuName, original_cmd, *args):
        """
        增强的菜单回调函数，先执行原始命令，再添加自定义菜单项
        """
        print("[增强回调] 菜单: {}, 原始命令: {}".format(menuName, original_cmd))
        
        # 先执行原始的菜单命令（如果存在）
        print("[增强回调] 执行原始菜单命令: {0}， is strip: {1}".format(original_cmd, original_cmd.strip()))
        if original_cmd:
            try:
                # 如果是字符串命令，直接执行
                if isinstance(original_cmd, str):
                    print("[增强回调] 执行原始MEL命令: {}".format(original_cmd))
                    mel.eval(original_cmd)
                # 如果是函数对象，调用它
                elif callable(original_cmd):
                    print("[增强回调] 执行原始Python回调")
                    original_cmd(*args)
            except Exception as e:
                print("[增强回调] 执行原始命令失败: {}".format(e))
        
        # 然后添加我们的自定义菜单项
        self._menu_callback(menuName, *args)
    
    def _menu_callback(self, menuName, *args):
        """菜单回调函数，根据配置动态添加菜单项"""
        print("[菜单回调] 弹出菜单: {}".format(menuName))
        self.clear_custom_menu_items(menuName)

        # 获取所有选择信息
        all_sels, sel_count = resolve_selection_type(True)
        if sel_count == 0:
            print("[菜单回调] 未选中任何对象，不添加自定义菜单")
            return

        print("[菜单回调] 选中数量: {} -> {}".format(sel_count, all_sels))

        # 初始化菜单项列表
        if menuName not in self.created_menu_items:
            self.created_menu_items[menuName] = []
        
        # 根据配置添加菜单项
        added_count = 0
        for config in self.menu_config:
            sel_type = config.get("selection_type")
            comp_type_needed = config.get("component_type")
            required_count = config.get("selected_obj_num")

            # 要求：在当前选择集中，至少存在一个对象匹配 selection_type / component_type
            has_match = False
            for so, ot, ct in all_sels:
                if ot != sel_type:
                    continue
                if comp_type_needed is not None and ct != comp_type_needed:
                    continue
                has_match = True
                break
            if not has_match:
                continue

            # 校验所需的选中数量
            if required_count is not None and sel_count != required_count:
                continue

            try:
                cmds.menuItem(parent=menuName, divider=True)
                added_item = cmds.menuItem(
                    label=config["label"],
                    parent=menuName,
                    annotation=self.CUSTOM_MENU_TAG,
                    command=partial(self._execute_command, config["command"])
                )
                
                # 记录创建的菜单项
                self.created_menu_items[menuName].append(added_item)
                added_count += 1
                print("[菜单回调] 添加菜单项: {} -> {}".format(config["label"], added_item))
                
            except Exception as e:
                print("[菜单回调] 添加菜单项失败: {}, 错误: {}".format(config["label"], e))
        
        if added_count == 0:
            print("[菜单回调] 没有匹配的菜单项可添加")
        else:
            print("[菜单回调] 共添加了 {} 个菜单项".format(added_count))
    
    def _execute_command(self, command_func, *args):
        """执行菜单命令"""
        try:
            print("[命令执行] 执行菜单命令，选择列表: {}".format(args))
            try:
                # 优先按带参数方式调用（传入选择列表）
                command_func(args)
            except TypeError:
                # 兼容无参命令
                command_func()
        except Exception as e:
            print("[命令执行] 执行失败: {}".format(e))
            cmds.inViewMessage(amg="命令执行失败: " + str(e), pos='topCenter', fade=True)

    def bind(self):
        """
        遍历所有modelPanel，绑定它们的ObjectPop菜单回调。
        绑定前先解绑之前绑定的菜单，避免重复。
        """
        print("[绑定] 开始绑定所有modelPanel的ObjectPop菜单")

        # 先解绑
        self.unbind()

        panels = cmds.getPanel(type='modelPanel')
        if not panels:
            print("[绑定] 未找到任何modelPanel，绑定失败")
            return False

        count = 0
        for panel in panels:
            menuName = panel + "ObjectPop"
            # 检查菜单是否存在
            if cmds.popupMenu(menuName, exists=True):
                try:
                    # 使用 cmds.menu 查询 postMenuCommand（参考 dagmenu.py）
                    menu_cmd = cmds.menu(menuName, query=True, postMenuCommand=True) or ""
                    self.original_menu_commands[menuName] = menu_cmd
                    print("[绑定] 保存原始菜单命令：{} -> {}".format(menuName, menu_cmd))
                    if menu_cmd:
                        # Maya's dag menu post command has the parent menu in it
                        parent_menu = menu_cmd.split(" ")[-1]
                        print("[绑定] 父菜单：{}, this menu name: {}".format(parent_menu, menuName))
                        
                        # 设置新的回调，使用 cmds.menu 设置（参考 dagmenu.py）
                        new_callback = partial(self._enhanced_menu_callback, menuName, menu_cmd)
                        cmds.menu(menuName, edit=True, postMenuCommand=new_callback)
                    
                    self.bound_menus.add(menuName)
                    count += 1
                    print("[绑定] 成功绑定菜单回调：{}".format(menuName))
                except Exception as e:
                    print("[绑定] 绑定菜单 {} 时出错：{}".format(menuName, e))
            else:
                print("[绑定] 菜单不存在，跳过：{}".format(menuName))

        print("[绑定] 完成，绑定了 {} 个菜单".format(count))
        return True

    def unbind(self):
        """
        解绑之前绑定的所有菜单回调，恢复原始的postMenuCommand，并清理自定义菜单项
        """
        if not self.bound_menus:
            print("[解绑] 当前无绑定菜单，跳过")
            return

        for menuName in list(self.bound_menus):
            if cmds.popupMenu(menuName, exists=True):
                try:
                    # 恢复原始的postMenuCommand，使用 MEL 命令（参考 dagmenu.py）
                    original_cmd = self.original_menu_commands.get(menuName, "")
                    if original_cmd and "buildObjectMenuItemsNow" in original_cmd:
                        print("[解绑] 恢复原始菜单命令：{} -> {}".format(menuName, original_cmd))
                        # 从原始命令中提取 parent_menu 路径
                        # 格式: "buildObjectMenuItemsNow MainPane|viewPanes|modelPanel4|modelPanel4|modelPanel4|modelPanel4ObjectPop"
                        parent_menu = original_cmd.split(" ")[-1]
                        
                        # 使用正确的 MEL 命令格式恢复原始的 postMenuCommand
                        # 格式: menu -edit -postMenuCommand "buildObjectMenuItemsNow parent_menu" menuName
                        mel.eval(
                            'menu -edit -postMenuCommand '
                            '"buildObjectMenuItemsNow ' + parent_menu.replace('"', '') + '" '
                            + menuName
                        )
                    else:
                        print("[解绑] 清空菜单命令：{}".format(menuName))
                        # 清空 postMenuCommand
                        mel.eval(
                            'menu -edit -postMenuCommand "" '
                            + menuName
                        )
                    
                    print("[解绑] 成功解绑菜单回调：{}".format(menuName))
                except Exception as e:
                    print("[解绑] 解绑菜单 {} 时出错：{}".format(menuName, e))

                self.clear_custom_menu_items(menuName)
            else:
                print("[解绑] 菜单不存在，跳过解绑：{}".format(menuName))

            self.bound_menus.remove(menuName)
        
        # 清空所有记录
        self.created_menu_items.clear()
        self.original_menu_commands.clear()
        print("[解绑] 完成解绑所有菜单，恢复了原始菜单命令，清理了所有记录")

    def __del__(self):
        """析构函数，确保实例销毁时清理所有菜单"""
        try:
            print("[析构] 菜单管理器实例即将销毁，开始清理...")
            self.unbind()
            print("[析构] 菜单管理器清理完成")
        except Exception as e:
            print("[析构] 清理过程中出错: {}".format(e))

    def add_menu_config(self, label, selection_type, component_type=None, command=None):
        """动态添加菜单配置"""
        if command is None:
            command = lambda obj: cmds.inViewMessage(amg="执行了菜单: " + label, pos='topCenter', fade=True)
        
        config = {
            "label": label,
            "selection_type": selection_type,
            "component_type": component_type,
            "command": command
        }
        
        self.menu_config.append(config)
        print("[配置] 添加菜单配置: {}".format(label))
    
    def remove_menu_config(self, label):
        """移除菜单配置"""
        self.menu_config = [cfg for cfg in self.menu_config if cfg["label"] != label]
        print("[配置] 移除菜单配置: {}".format(label))

# ---------- 测试窗口类 ----------

class MenuTestWindow(object):
    """
    菜单测试窗口，用于测试菜单绑定和解绑功能
    窗口关闭时会自动清理菜单管理器实例
    """
    
    def __init__(self):
        self.window_name = "MenuTestWindow"
        self.menu_manager = None
        self.create_window()
    
    def create_window(self):
        """创建测试窗口"""
        # 如果窗口已存在，先删除
        if cmds.window(self.window_name, exists=True):
            cmds.deleteUI(self.window_name)
        
        # 创建窗口
        self.window = cmds.window(
            self.window_name,
            title="自定义右键菜单测试工具",
            widthHeight=(400, 200),
            sizeable=False
        )
        
        # 创建布局
        main_layout = cmds.columnLayout(adjustableColumn=True, rowSpacing=10)
        
        # 标题
        cmds.text(label="GWrap变形器 - 右键菜单测试", height=30, font="boldLabelFont")
        cmds.separator(height=10)
        
        # 状态显示
        self.status_text = cmds.text(label="状态: 未绑定", height=25, backgroundColor=[0.2, 0.2, 0.2])
        cmds.separator(height=10)
        
        # 按钮布局
        button_layout = cmds.rowLayout(numberOfColumns=4, columnWidth4=(80, 80, 80, 80))
        
        # 绑定按钮
        self.bind_button = cmds.button(
            label="绑定自定义菜单",
            command=self.bind_menu,
            backgroundColor=[0.3, 0.7, 0.3]
        )
        
        # 解绑按钮
        self.unbind_button = cmds.button(
            label="解绑自定义菜单",
            command=self.unbind_menu,
            backgroundColor=[0.7, 0.3, 0.3],
            enable=False
        )

        self.test_curve_func_btn = cmds.button(
            label="测试曲线功能",
            command=self.test_curve_func,
            backgroundColor=[0.3, 0.7, 0.3]
        )   
        
        self.test_deformer_func_btn = cmds.button(
            label="测试变形器功能",
            command=self.test_deformer_func,
            backgroundColor=[0.3, 0.7, 0.3]
        )

        cmds.setParent('..')  # 回到主布局
        cmds.separator(height=15)
        
        # 说明文字
        cmds.text(label="说明：", align="left", font="boldLabelFont")
        cmds.text(label="• 点击'绑定'后，右键点击模型或面可看到自定义菜单", align="left")
        cmds.text(label="• 关闭此窗口会自动解绑所有菜单", align="left")
        cmds.text(label="• 支持mesh对象、face组件、nurbsCurve等", align="left")
        
        # 设置窗口关闭回调
        cmds.window(self.window, edit=True, closeCommand=self.on_window_close)
        
        # 显示窗口
        cmds.showWindow(self.window)
        
        print("[测试窗口] 菜单测试窗口已创建")
    
    def bind_menu(self, *args):
        """绑定菜单"""
        try:
            # 创建新的菜单管理器实例
            self.menu_manager = AllViewportObjectMenuManager()
            success = self.menu_manager.bind()
            
            if success:
                cmds.text(self.status_text, edit=True, label="状态: 已绑定", backgroundColor=[0.3, 0.7, 0.3])
                cmds.button(self.bind_button, edit=True, enable=False)
                cmds.button(self.unbind_button, edit=True, enable=True)
                print("[测试窗口] 菜单绑定成功")
            else:
                cmds.text(self.status_text, edit=True, label="状态: 绑定失败", backgroundColor=[0.7, 0.3, 0.3])
                print("[测试窗口] 菜单绑定失败")
                
        except Exception as e:
            cmds.text(self.status_text, edit=True, label="状态: 绑定出错", backgroundColor=[0.7, 0.3, 0.3])
            print("[测试窗口] 绑定出错: {}".format(e))
    
    def unbind_menu(self, *args):
        """解绑菜单"""
        try:
            if self.menu_manager:
                self.menu_manager.unbind()
                self.menu_manager = None
                
            cmds.text(self.status_text, edit=True, label="状态: 已解绑", backgroundColor=[0.2, 0.2, 0.2])
            cmds.button(self.bind_button, edit=True, enable=True)
            cmds.button(self.unbind_button, edit=True, enable=False)
            print("[测试窗口] 菜单解绑成功")
            
        except Exception as e:
            print("[测试窗口] 解绑出错: {}".format(e))
    
    def test_curve_func(self, *args):
        print("test_curve_func")
        create_curve_from_sels(None)    

    def test_deformer_func(self, *args):
        print("test_deformer_func")
        create_deformer_from_curve(None)

    def on_window_close(self, *args):
        """窗口关闭时的回调"""
        print("[测试窗口] 窗口即将关闭，开始清理...")
        try:
            if self.menu_manager:
                print("[测试窗口] 自动解绑菜单管理器")
                self.menu_manager.unbind()
                self.menu_manager = None
            print("[测试窗口] 清理完成")
        except Exception as e:
            print("[测试窗口] 清理过程中出错: {}".format(e))


# ---------- 主函数 ----------

def show_test_window():
    """显示测试窗口"""
    MenuTestWindow()


# ---------- 测试调用示例 ----------

if __name__=="__main__":
    # 显示测试窗口
    show_test_window()