# -*- coding: utf-8 -*-
"""
Reference Image Plane utilities for Maya view switching and image plane management.
"""
# Import built-in modules
import logging
import math

# Import third-party modules
import maya.cmds as cmds

# Import local modules
from cgame_avatar_factory.common import constants as const

# Constants for image plane property mapping
PROPERTY_MAPPING = {
    "大小": {"set_attr": "size", "get_attr": "sizeX", "type": "both"},  # Set both sizeX/sizeY, get sizeX
    "不透明度": {"set_attr": "alphaGain", "get_attr": "alphaGain", "type": "single"},
    "水平偏移": {"set_attr": "offsetX", "get_attr": "offsetX", "type": "single"},
    "垂直偏移": {"set_attr": "offsetY", "get_attr": "offsetY", "type": "single"},
    "旋转": {"set_attr": "rotate", "get_attr": "rotate", "type": "shape"},  # Shape node operations
}

# Default values for properties
PROPERTY_DEFAULTS = {
    "大小": 1.0,
    "不透明度": 0.5,
    "水平偏移": 0.0,
    "垂直偏移": 0.0,
    "旋转": 0.0,
}

# Orthographic view names (excluding perspective)
# Update: remove "左" and "俯", add intermediate angle views
ORTHOGRAPHIC_VIEWS = ["正", "30°", "40°", "右"]

# Named constants (defined after ORTHOGRAPHIC_VIEWS)
ORTHOGRAPHIC_VIEW_SET = set(ORTHOGRAPHIC_VIEWS)
ANGLE_VIEWS = ("30°", "40°")
CAMERA_DEFAULT_DIST = 1000.0
ORTHO_WIDTH_DEFAULT = 10
ROTATE_OFFSET_LIMIT_DEG = 15.0

# Module-level state for view access tracking
_view_access_history = {}

_logger = logging.getLogger(__name__)


def _is_first_view_access(view_name):
    """Check if this is the first time accessing the specified view.

    检查是否是第一次访问指定视图。

    Args:
        view_name (str): View name ("正", "30°", "40°", "右", "透视")

    Returns:
        bool: True if this is the first access, False otherwise
    """
    return not _view_access_history.get(view_name, False)


def _mark_view_accessed(view_name):
    """Mark the specified view as accessed.

    标记指定视图已被访问。

    Args:
        view_name (str): View name ("正", "30°", "40°", "右", "透视")
    """
    _view_access_history[view_name] = True


def reset_view_access_history():
    """Reset view access history, making all views behave as first-time access.

    重置视图访问历史，使所有视图都表现为首次访问。
    """
    global _view_access_history
    _view_access_history = {}
    _logger.info("View access history has been reset")


def switch_to_front_view():
    """Switch Maya viewport to front view and focus on head model.

    切换Maya视口到正视图并聚焦到头部模型。
    """
    try:
        view_name = "正"
        is_first_access = _is_first_view_access(view_name)

        panel = _get_active_model_panel()
        if panel:
            current_camera = cmds.modelPanel(panel, query=True, camera=True)
            if current_camera == "front":
                # Only focus on first access when already in the view
                if is_first_access:
                    focus_on_head_model()
                    _mark_view_accessed(view_name)
                return
            cmds.modelEditor(panel, edit=True, camera="front")
        else:
            cmds.viewSet(front=True)

        # Only focus on first access when switching to the view
        if is_first_access:
            focus_on_head_model()
            _mark_view_accessed(view_name)

    except Exception as e:
        _logger.error(f"Failed to switch to front view: {e}")


def switch_to_left_view():
    """Switch Maya viewport to left view and focus on head model.

    切换Maya视口到左视图并聚焦到头部模型。
    """
    try:
        view_name = "左"
        is_first_access = _is_first_view_access(view_name)

        # Ensure left camera exists
        ensure_left_camera_exists()

        panel = _get_active_model_panel()
        if panel:
            current_camera = cmds.modelPanel(panel, query=True, camera=True)
            if current_camera == "left":
                # Only focus on first access when already in the view
                if is_first_access:
                    focus_on_head_model()
                    _mark_view_accessed(view_name)
                return
            cmds.modelEditor(panel, edit=True, camera="left")
        else:
            # Force switch to left view instead of side view
            cmds.lookThru("left")
            # Refresh viewport to ensure camera switch is complete
            cmds.refresh()

        # Only focus on first access when switching to the view
        if is_first_access:
            focus_on_head_model()
            _mark_view_accessed(view_name)

    except Exception as e:
        _logger.error(f"Failed to switch to left view: {e}")


def switch_to_right_view():
    """Switch Maya viewport to right view and focus on head model.

    切换Maya视口到右视图并聚焦到头部模型。
    """
    try:
        view_name = "右"
        is_first_access = _is_first_view_access(view_name)

        panel = _get_active_model_panel()
        if panel:
            current_camera = cmds.modelPanel(panel, query=True, camera=True)
            if current_camera == "side":
                # Only focus on first access when already in the view
                if is_first_access:
                    focus_on_head_model()
                    _mark_view_accessed(view_name)
                return
            cmds.modelEditor(panel, edit=True, camera="side")
        else:
            cmds.viewSet(side=True)

        # Only focus on first access when switching to the view
        if is_first_access:
            focus_on_head_model()
            _mark_view_accessed(view_name)

    except Exception as e:
        _logger.error(f"Failed to switch to right view: {e}")


def switch_to_top_view():
    """Switch Maya viewport to top view and focus on head model.

    切换Maya视口到俯视图并聚焦到头部模型。
    """
    try:
        view_name = "俯"
        is_first_access = _is_first_view_access(view_name)

        panel = _get_active_model_panel()
        if panel:
            current_camera = cmds.modelPanel(panel, query=True, camera=True)
            if current_camera == "top":
                # Only focus on first access when already in the view
                if is_first_access:
                    focus_on_head_model()
                    _mark_view_accessed(view_name)
                return
            cmds.modelEditor(panel, edit=True, camera="top")
        else:
            cmds.viewSet(top=True)

        # Only focus on first access when switching to the view
        if is_first_access:
            focus_on_head_model()
            _mark_view_accessed(view_name)

    except Exception as e:
        _logger.error(f"Failed to switch to top view: {e}")


def switch_to_perspective_view():
    """Switch Maya viewport to perspective view and focus on head model.

    切换Maya视口到透视图并聚焦到头部模型。
    """
    try:
        view_name = "透视"
        is_first_access = _is_first_view_access(view_name)

        panel = _get_active_model_panel()

        if panel:
            current_camera = cmds.modelPanel(panel, query=True, camera=True)
            if current_camera == "persp":
                # Only focus on first access when already in the view
                if is_first_access:
                    focus_on_head_model()
                    _mark_view_accessed(view_name)
                # Hide all image planes when already in perspective view
                hide_all_image_planes()
                return
            cmds.modelEditor(panel, edit=True, camera="persp")
        else:
            cmds.lookThru("persp")

        # Only focus on first access when switching to the view
        if is_first_access:
            focus_on_head_model()
            _mark_view_accessed(view_name)

        # Hide all image planes when switching to perspective view
        hide_all_image_planes()

    except Exception as e:
        _logger.error(f"Failed to switch to perspective view: {e}")


def _get_active_model_panel():
    """Get the currently active model panel.

    获取当前活动的模型面板。

    Returns:
        str: Active model panel name, or None if not found
    """
    try:
        # First try to get the panel with focus
        panel = cmds.getPanel(withFocus=True)
        if cmds.getPanel(typeOf=panel) == "modelPanel":
            return panel

        # If focused panel is not a model panel, try to find the main viewport
        main_viewports = ["modelPanel4", "modelPanel1", "modelPanel2", "modelPanel3"]
        for main_panel in main_viewports:
            if cmds.modelPanel(main_panel, exists=True):
                return main_panel

        # If main viewports don't exist, find any model panel
        panels = cmds.getPanel(type="modelPanel")
        if panels:
            return panels[0]

        return None

    except Exception as e:
        _logger.warning(f"Failed to get active model panel: {e}")
        return None


def ensure_left_camera_exists():
    """Ensure left camera exists in the scene.

    确保场景中存在left相机。
    """
    try:
        # Check if left camera already exists
        if cmds.objExists("left"):
            return

        # Create left camera if it doesn't exist
        camera_result = cmds.camera()
        left_camera_transform = camera_result[0]

        # Rename the camera to 'left'
        left_camera_transform = cmds.rename(left_camera_transform, "left")

        # Get the shape node after renaming (it will be automatically renamed too)
        camera_shapes = cmds.listRelatives(left_camera_transform, shapes=True, type="camera")
        if not camera_shapes:
            raise Exception("Failed to find camera shape after creation")
        left_camera_shape = camera_shapes[0]

        # Set left camera to orthographic view
        cmds.setAttr(f"{left_camera_shape}.orthographic", 1)

        # Get scene up axis and set camera position accordingly
        up_axis = cmds.upAxis(query=True, axis=True)

        if up_axis == "z":
            # Z-up scene
            cmds.setAttr(f"{left_camera_transform}.translateX", -1000)
            cmds.setAttr(f"{left_camera_transform}.translateY", 0)
            cmds.setAttr(f"{left_camera_transform}.translateZ", 0)
            cmds.setAttr(f"{left_camera_transform}.rotateX", 90)
            cmds.setAttr(f"{left_camera_transform}.rotateY", 0)
            cmds.setAttr(f"{left_camera_transform}.rotateZ", -90)
        else:
            # Y-up scene (Maya default)
            cmds.setAttr(f"{left_camera_transform}.translateX", -1000)
            cmds.setAttr(f"{left_camera_transform}.translateY", 0)
            cmds.setAttr(f"{left_camera_transform}.translateZ", 0)
            cmds.setAttr(f"{left_camera_transform}.rotateX", 0)
            cmds.setAttr(f"{left_camera_transform}.rotateY", -90)
            cmds.setAttr(f"{left_camera_transform}.rotateZ", 0)

        # Set orthographic width for proper framing
        cmds.setAttr(f"{left_camera_shape}.orthographicWidth", ORTHO_WIDTH_DEFAULT)

    except Exception as e:
        _logger.error(f"Failed to create left camera: {e}")


def ensure_angle_camera_exists(angle_name, angle_deg):
    """Ensure a custom orthographic camera exists at a given yaw angle between front(0) and right(90).

    确保存在一个介于正视(0°)与右视(90°)之间的自定义正交相机。

    Args:
        angle_name (str): Camera transform name, e.g. "angle30", "angle40"
        angle_deg (float): Yaw angle in degrees (0-90)
    """
    try:
        # If exists, just ensure it's orthographic and roughly oriented; skip heavy updates to not fight user edits
        if cmds.objExists(angle_name):
            # Ensure shape exists and is orthographic
            camera_shapes = cmds.listRelatives(angle_name, shapes=True, type="camera") or []
            if camera_shapes:
                cmds.setAttr(f"{camera_shapes[0]}.orthographic", 1)
                return

        # Create camera
        cam_result = cmds.camera()
        cam_transform = cam_result[0]
        cam_transform = cmds.rename(cam_transform, angle_name)

        # Get shape
        camera_shapes = cmds.listRelatives(cam_transform, shapes=True, type="camera")
        if not camera_shapes:
            raise Exception("Failed to find camera shape after creation")
        cam_shape = camera_shapes[0]

        # Set orthographic
        cmds.setAttr(f"{cam_shape}.orthographic", 1)
        cmds.setAttr(f"{cam_shape}.orthographicWidth", ORTHO_WIDTH_DEFAULT)

        # Place and orient according to scene up axis
        up_axis = cmds.upAxis(query=True, axis=True)

        rad = math.radians(angle_deg)
        dist = CAMERA_DEFAULT_DIST

        if up_axis == "z":
            # Z-up: rotate around Z; keep camera level by rotateX 90 similar to left setup; translate in XY plane
            tx = dist * math.sin(rad)
            ty = dist * math.cos(rad)
            cmds.setAttr(f"{cam_transform}.translateX", tx)
            cmds.setAttr(f"{cam_transform}.translateY", ty)
            cmds.setAttr(f"{cam_transform}.translateZ", 0)
            cmds.setAttr(f"{cam_transform}.rotateX", 90)
            cmds.setAttr(f"{cam_transform}.rotateY", 0)
            cmds.setAttr(f"{cam_transform}.rotateZ", angle_deg)
        else:
            # Y-up: rotate around Y; translate in XZ plane
            tx = dist * math.sin(rad)
            tz = dist * math.cos(rad)
            cmds.setAttr(f"{cam_transform}.translateX", tx)
            cmds.setAttr(f"{cam_transform}.translateY", 0)
            cmds.setAttr(f"{cam_transform}.translateZ", tz)
            cmds.setAttr(f"{cam_transform}.rotateX", 0)
            cmds.setAttr(f"{cam_transform}.rotateY", angle_deg)
            cmds.setAttr(f"{cam_transform}.rotateZ", 0)

    except Exception as e:
        _logger.error(f"Failed to create angle camera {angle_name}: {e}")


def switch_to_30deg_view():
    """Switch to custom 30° orthographic view (between front and right)."""
    try:
        view_name = "30°"
        is_first_access = _is_first_view_access(view_name)

        ensure_angle_camera_exists("angle30", 30.0)

        panel = _get_active_model_panel()
        if panel:
            current_camera = cmds.modelPanel(panel, query=True, camera=True)
            if current_camera == "angle30":
                if is_first_access:
                    focus_on_head_model()
                    _mark_view_accessed(view_name)
                return
            cmds.modelEditor(panel, edit=True, camera="angle30")
        else:
            cmds.lookThru("angle30")

        if is_first_access:
            focus_on_head_model()
            _mark_view_accessed(view_name)

    except Exception as e:
        _logger.error(f"Failed to switch to 30° view: {e}")


def switch_to_40deg_view():
    """Switch to custom 40° orthographic view (between front and right)."""
    try:
        view_name = "40°"
        is_first_access = _is_first_view_access(view_name)

        ensure_angle_camera_exists("angle40", 40.0)

        panel = _get_active_model_panel()
        if panel:
            current_camera = cmds.modelPanel(panel, query=True, camera=True)
            if current_camera == "angle40":
                if is_first_access:
                    focus_on_head_model()
                    _mark_view_accessed(view_name)
                return
            cmds.modelEditor(panel, edit=True, camera="angle40")
        else:
            cmds.lookThru("angle40")

        if is_first_access:
            focus_on_head_model()
            _mark_view_accessed(view_name)

    except Exception as e:
        _logger.error(f"Failed to switch to 40° view: {e}")


def focus_on_head_model():
    """Focus camera view on head model.

    聚焦相机视图到头部模型。
    """
    try:
        # Clear current selection
        cmds.select(clear=True)

        # Try to focus on backup head mesh first, then base head mesh
        focus_object = None
        if cmds.objExists(const.BAK_HEAD_MESH_NAME):
            focus_object = const.BAK_HEAD_MESH_NAME
        elif cmds.objExists(const.BASE_HEAD_MESH_NAME):
            focus_object = const.BASE_HEAD_MESH_NAME

        if focus_object:
            cmds.select(focus_object, replace=True)
            cmds.viewFit(animate=False)
            cmds.select(clear=True)
        else:
            # If no specific head mesh found, try to focus on all visible meshes
            visible_meshes = cmds.ls(type="mesh", visible=True)
            if visible_meshes:
                mesh_transforms = cmds.listRelatives(visible_meshes, parent=True, fullPath=True) or []
                if mesh_transforms:
                    cmds.select(mesh_transforms)
                    cmds.viewFit(animate=True)
                    cmds.select(clear=True)

        # Refresh the viewport
        cmds.refresh()

    except Exception as e:
        _logger.warning(f"Failed to focus on head model: {e}")


def get_view_name_mapping():
    """Get mapping between UI view names and switch functions.

    获取UI视图名称和切换函数的映射关系。

    Returns:
        dict: Mapping of view names to switch functions
    """
    return {
        "正": switch_to_front_view,
        "30°": switch_to_30deg_view,
        "40°": switch_to_40deg_view,
        "右": switch_to_right_view,
        "透视": switch_to_perspective_view,  # For cube button
    }


def get_current_view_info():
    """Get information about the current view.

    获取当前视图信息。

    Returns:
        dict: Current view information including panel and camera
    """
    try:
        panel = _get_active_model_panel()
        if panel:
            camera = cmds.modelPanel(panel, query=True, camera=True)
            return {
                "panel": panel,
                "camera": camera,
                "view_type": _get_view_type_from_camera(camera),
            }
        return {"panel": None, "camera": None, "view_type": "unknown"}
    except Exception as e:
        _logger.warning(f"Failed to get current view info: {e}")
        return {"panel": None, "camera": None, "view_type": "error"}


# Centralized view-camera mapping
VIEW_CAMERA_MAPPING = {
    "正视": "front",
    "右视": "side",
    # Support simplified names from UI
    "正": "front",
    "右": "side",
    "30°": "angle30",
    "40°": "angle40",
}

CAMERA_VIEW_MAPPING = {
    "front": "正",
    "side": "右",
    "angle30": "30°",
    "angle40": "40°",
    "persp": "透视",
}

CAMERA_DESCRIPTION_MAPPING = {
    "persp": "透视图",
    "front": "正视图",
    "side": "右视图",
    "angle30": "30°视图",
    "angle40": "40°视图",
}


def _get_view_type_from_camera(camera):
    """Get view type description from camera name.

    根据相机名称获取视图类型描述。

    Args:
        camera (str): Camera name

    Returns:
        str: View type description
    """
    return CAMERA_DESCRIPTION_MAPPING.get(camera, f"自定义视图({camera})")


def switch_view_by_name(view_name):
    """Switch view by UI view name.

    根据UI视图名称切换视图。

    Args:
        view_name (str): View name from UI ("正视", "左视", "右视", "俯视")
    """
    view_mapping = get_view_name_mapping()

    if view_name in view_mapping:
        # Switch to the view
        view_mapping[view_name]()

        # Hide other image planes and show current view's image planes
        hide_all_other_image_planes(view_name)
    else:
        _logger.warning(f"Unknown view name: {view_name}")


def _get_camera_name_from_view(view_name):
    """Get camera name from view name.

    根据视图名称获取相机名称。

    Args:
        view_name (str): View name ("正视", "右视", "正", "30°", "40°", "右")

    Returns:
        str: Camera name or None if not found
    """
    return VIEW_CAMERA_MAPPING.get(view_name)


def _get_image_plane_shape_node(plane_name):
    """Get the shape node for an image plane.

    获取image plane的shape节点。

    Args:
        plane_name (str): Image plane transform node name

    Returns:
        str: Shape node name, or None if not found
    """
    try:
        # Get shape nodes of type imagePlane
        shapes = cmds.listRelatives(plane_name, shapes=True, type="imagePlane")
        if shapes:
            return shapes[0]
        return None
    except Exception as e:
        _logger.warning(f"Failed to get shape node for {plane_name}: {e}")
        return None


def _find_image_planes_for_camera(camera_name):
    """Find all image planes connected to the specified camera.

    查找连接到指定相机的所有图片平面。

    Args:
        camera_name (str): Camera name (front, left, side, top)

    Returns:
        list: List of image plane transform node names connected to the camera
    """
    try:
        # Get all imagePlane shape nodes
        image_plane_shapes = cmds.ls(type="imagePlane")
        connected_planes = []

        for shape in image_plane_shapes:
            try:
                # Check if this image plane is connected to our target camera
                connections = cmds.listConnections(f"{shape}.message", type="camera")

                if connections and camera_name in connections:
                    # Get the transform node (parent) of the shape
                    transform_nodes = cmds.listRelatives(shape, parent=True, type="transform")
                    if transform_nodes:
                        connected_planes.append(transform_nodes[0])

            except Exception as e:
                _logger.warning(f"Error checking image plane {shape}: {e}")
                continue

        return connected_planes

    except Exception as e:
        _logger.error(f"Failed to find image planes for camera {camera_name}: {e}")
        return []


def create_image_plane(image_path, view_name):
    """Create an image plane for the specified view.

    为指定视图创建image plane。

    Args:
        image_path (str): Path to the image file
        view_name (str): View name ("正视", "左视", "右视", "俯视")

    Returns:
        str: Name of the created image plane, or None if failed
    """
    try:
        camera_name = _get_camera_name_from_view(view_name)
        if not camera_name:
            _logger.error(f"Unknown view name for image plane: {view_name}")
            return None

        # Delete existing image plane for this view first
        delete_image_plane_for_view(view_name)

        # Create image plane with English name to avoid Maya warnings
        # Use camera name for consistent naming
        image_plane_name = f"imagePlane_{camera_name}"
        image_plane = cmds.imagePlane(
            fileName=image_path,
            camera=camera_name,
            name=image_plane_name,
        )

        # Set default properties
        if image_plane:
            # Set initial size and opacity
            cmds.setAttr(f"{image_plane[0]}.sizeX", 1.0)
            cmds.setAttr(f"{image_plane[0]}.sizeY", 1.0)
            cmds.setAttr(f"{image_plane[0]}.alphaGain", 0.5)
            cmds.setAttr(f"{image_plane[0]}.depth", 100.0)

            # Set default rotation on shape node
            shape_node = _get_image_plane_shape_node(image_plane[0])
            if shape_node:
                cmds.setAttr(f"{shape_node}.rotate", 0.0)

            _logger.info(f"Created image plane {image_plane[0]} for {view_name} view")
            return image_plane[0]

        return None

    except Exception as e:
        _logger.error(f"Failed to create image plane for {view_name}: {e}")
        return None


def delete_image_plane_for_view(view_name):
    """Delete image plane for the specified view.

    删除指定视图的image plane。

    Args:
        view_name (str): View name ("正视", "左视", "右视", "俯视")

    Returns:
        bool: True if deleted successfully, False otherwise
    """
    try:
        camera_name = _get_camera_name_from_view(view_name)
        if not camera_name:
            _logger.error(f"Unknown view name for image plane deletion: {view_name}")
            return False

        # Find and delete image planes for this camera
        connected_planes = _find_image_planes_for_camera(camera_name)

        deleted_count = 0
        for plane in connected_planes:
            try:
                cmds.delete(plane)
                deleted_count += 1
            except Exception as e:
                _logger.warning(f"Failed to delete image plane {plane}: {e}")

        return deleted_count > 0

    except Exception as e:
        _logger.error(f"Failed to delete image plane for {view_name}: {e}")
        return False


def get_image_planes_for_view(view_name):
    """Get list of image planes for the specified view.

    获取指定视图的image plane列表。

    Args:
        view_name (str): View name ("正视", "左视", "右视", "俯视")

    Returns:
        list: List of image plane names
    """
    try:
        camera_name = _get_camera_name_from_view(view_name)
        if not camera_name:
            return []

        # Use the shared function to find image planes
        view_planes = _find_image_planes_for_camera(camera_name)
        return view_planes

    except Exception as e:
        _logger.error(f"Failed to get image planes for {view_name}: {e}")
        return []


def is_perspective_view():
    """Check if current view is perspective view.

    检查当前视图是否为透视图。

    Returns:
        bool: True if current view is perspective, False otherwise
    """
    try:
        view_info = get_current_view_info()
        return view_info.get("camera") == "persp"
    except Exception as e:
        _logger.warning(f"Failed to check if perspective view: {e}")
        return False


def get_current_view_name():
    """Get current view name in UI format.

    获取当前视图的UI格式名称。

    Returns:
        str: View name ("正", "30°", "40°", "右", "透视") or None if unknown
    """
    try:
        view_info = get_current_view_info()
        camera = view_info.get("camera")
        return CAMERA_VIEW_MAPPING.get(camera)
    except Exception as e:
        _logger.warning(f"Failed to get current view name: {e}")
        return None


def set_image_plane_property(view_name, property_name, value):
    """Set image plane property for the specified view.

    为指定视图的image plane设置属性。

    Args:
        view_name (str): View name ("正", "30°", "40°", "右")
        property_name (str): Property name ("大小", "不透明度", "水平偏移", "垂直偏移")
        value (float): Property value

    Returns:
        bool: True if property was set successfully, False otherwise
    """
    try:
        # Get image planes for this view
        image_planes = get_image_planes_for_view(view_name)

        if not image_planes:
            _logger.warning(f"No image planes found for {view_name} view")
            return False

        if property_name not in PROPERTY_MAPPING:
            _logger.error(f"Unknown property name: {property_name}")
            return False

        prop_info = PROPERTY_MAPPING[property_name]
        success_count = 0

        for plane in image_planes:
            try:
                if prop_info["type"] == "both":
                    # For size, set both X and Y
                    cmds.setAttr(f"{plane}.sizeX", value)
                    cmds.setAttr(f"{plane}.sizeY", value)
                elif prop_info["type"] == "shape":
                    # For shape attributes like rotate, set on shape node
                    shape_node = _get_image_plane_shape_node(plane)
                    if shape_node:
                        cmds.setAttr(f"{shape_node}.{prop_info['set_attr']}", value)
                    else:
                        _logger.warning(f"Shape node not found for {plane}")
                        continue
                else:
                    # For other properties, set single attribute
                    cmds.setAttr(f"{plane}.{prop_info['set_attr']}", value)

                success_count += 1

            except Exception as e:
                _logger.warning(f"Failed to set {property_name} for image plane {plane}: {e}")

        return success_count > 0

    except Exception as e:
        _logger.error(f"Failed to set image plane property {property_name} for {view_name}: {e}")
        return False


def get_image_plane_property(view_name, property_name):
    """Get image plane property for the specified view.

    获取指定视图的image plane属性。

    Args:
        view_name (str): View name ("正", "30°", "40°", "右")
        property_name (str): Property name ("大小", "不透明度", "水平偏移", "垂直偏移")

    Returns:
        float: Property value, or None if not found
    """
    try:
        # Get image planes for this view
        image_planes = get_image_planes_for_view(view_name)

        if not image_planes:
            return None

        if property_name not in PROPERTY_MAPPING:
            _logger.error(f"Unknown property name: {property_name}")
            return None

        prop_info = PROPERTY_MAPPING[property_name]

        # Get value from the first image plane
        plane = image_planes[0]
        try:
            if prop_info["type"] == "shape":
                # For shape attributes like rotate, get from shape node
                shape_node = _get_image_plane_shape_node(plane)
                if shape_node:
                    value = cmds.getAttr(f"{shape_node}.{prop_info['get_attr']}")
                    return value
                else:
                    _logger.warning(f"Shape node not found for {plane}")
                    return None
            else:
                value = cmds.getAttr(f"{plane}.{prop_info['get_attr']}")
                return value

        except Exception as e:
            _logger.warning(f"Failed to get {property_name} for image plane {plane}: {e}")
            return None

    except Exception as e:
        _logger.error(f"Failed to get image plane property {property_name} for {view_name}: {e}")
        return None


def get_all_image_plane_properties(view_name):
    """Get all image plane properties for the specified view.

    获取指定视图的所有image plane属性。

    Args:
        view_name (str): View name ("正", "30°", "40°", "右")

    Returns:
        dict: Dictionary with property names as keys and values as values
    """
    try:
        result = {}

        for prop_name in PROPERTY_MAPPING.keys():
            value = get_image_plane_property(view_name, prop_name)
            if value is not None:
                result[prop_name] = value
            else:
                # Set default values if property not found
                result[prop_name] = PROPERTY_DEFAULTS.get(prop_name, 0.0)

        return result

    except Exception as e:
        _logger.error(f"Failed to get all image plane properties for {view_name}: {e}")
        return PROPERTY_DEFAULTS.copy()


def set_image_plane_visibility(view_name, visible):
    """Set image plane visibility for the specified view.

    设置指定视图的image plane可见性。

    Args:
        view_name (str): View name ("正", "30°", "40°", "右")
        visible (bool): True to show, False to hide

    Returns:
        bool: True if visibility was set successfully, False otherwise
    """
    try:
        # Get image planes for this view
        image_planes = get_image_planes_for_view(view_name)

        if not image_planes:
            return True  # No image planes to hide/show, consider it successful

        success_count = 0
        for plane in image_planes:
            try:
                cmds.setAttr(f"{plane}.visibility", visible)
                success_count += 1
            except Exception as e:
                _logger.warning(f"Failed to set visibility for image plane {plane}: {e}")

        return success_count > 0

    except Exception as e:
        _logger.error(f"Failed to set image plane visibility for {view_name}: {e}")
        return False


def hide_all_other_image_planes(active_view_name):
    """Hide image planes for all views except the active one.

    隐藏除激活视图外所有其他视图的image plane。

    Args:
        active_view_name (str): Active view name ("正", "30°", "40°", "右", "透视")

    Returns:
        bool: True if operation was successful, False otherwise
    """
    try:
        # Hide image planes for all other views
        for view_name in ORTHOGRAPHIC_VIEWS:
            if view_name != active_view_name:
                set_image_plane_visibility(view_name, False)

        # Show image planes for the active view (if not perspective)
        if active_view_name and active_view_name != "透视":
            set_image_plane_visibility(active_view_name, True)

        return True

    except Exception as e:
        _logger.error(f"Failed to hide other image planes for active view {active_view_name}: {e}")
        return False


def hide_all_image_planes():
    """Hide all image planes in the scene.

    隐藏场景中的所有image plane。

    Returns:
        bool: True if operation was successful, False otherwise
    """
    try:
        # Get all imagePlane shape nodes and find their transform parents
        all_shape_planes = cmds.ls(type="imagePlane") or []
        all_transform_planes = []

        for shape in all_shape_planes:
            try:
                # Get the transform node (parent) of the shape
                transform_nodes = cmds.listRelatives(shape, parent=True, type="transform")
                if transform_nodes:
                    all_transform_planes.append(transform_nodes[0])
            except Exception as e:
                _logger.warning(f"Failed to get transform for shape {shape}: {e}")

        # Hide all transform nodes (consistent with set_image_plane_visibility)
        for plane in all_transform_planes:
            try:
                cmds.setAttr(f"{plane}.visibility", False)
            except Exception as e:
                _logger.warning(f"Failed to hide image plane {plane}: {e}")

        return True

    except Exception as e:
        _logger.error(f"Failed to hide all image planes: {e}")
        return False


def get_image_plane_image_path(view_name):
    """Get the image file path for the specified view's image plane.

    获取指定视图的image plane的图片文件路径。

    Args:
        view_name (str): View name ("正", "30°", "40°", "右")

    Returns:
        str: Image file path, or None if not found
    """
    try:
        # Get image planes for this view
        image_planes = get_image_planes_for_view(view_name)

        if not image_planes:
            return None

        # Get image path from the first image plane
        plane = image_planes[0]
        try:
            image_path = cmds.getAttr(f"{plane}.imageName")
            return image_path

        except Exception as e:
            _logger.warning(f"Failed to get image path for image plane {plane}: {e}")
            return None

    except Exception as e:
        _logger.error(f"Failed to get image plane image path for {view_name}: {e}")
        return None


# --- Ortho view rotation & reset (正 / 30° / 40° / 右) ---

# Store per-view offsets (degrees)
_angle_view_offsets = {
    "正": {"h": 0.0, "v": 0.0},
    "30°": {"h": 0.0, "v": 0.0},
    "40°": {"h": 0.0, "v": 0.0},
    "右": {"h": 0.0, "v": 0.0},
}

# Baseline (absolute) orientation per view: {'yaw': deg, 'pitch': deg, 'dist': float}
_angle_view_baseline = {
    "正": None,
    "30°": None,
    "40°": None,
    "右": None,
}


def _compute_yaw_pitch_dist_from_vector(vec, up_axis):
    # vec: camera_pos - pivot
    x, y, z = vec[0], vec[1], vec[2]
    if up_axis == "y":
        yaw = math.degrees(math.atan2(x, z))
        hyp = math.sqrt(x * x + z * z)
        pitch = math.degrees(math.atan2(y, hyp))
    else:  # z-up
        yaw = math.degrees(math.atan2(x, y))
        hyp = math.sqrt(x * x + y * y)
        pitch = math.degrees(math.atan2(z, hyp))
    dist = math.sqrt(x * x + y * y + z * z)
    return yaw, pitch, dist


def _ensure_angle_baseline(view_name, pivot):
    """Ensure baseline yaw/pitch/dist for a view is recorded.

    For 30°/40° views, force canonical yaw (30/40) with flat pitch as baseline to avoid 180° flips.
    """
    try:
        if _angle_view_baseline.get(view_name):
            return
        cam = _get_camera_name_from_view(view_name)
        if not cam or not cmds.objExists(cam):
            return
        cam_pos = cmds.xform(cam, q=True, ws=True, t=True)
        up_axis = cmds.upAxis(query=True, axis=True)
        if view_name in ("正", "30°", "40°", "右"):
            yaw0 = _get_angle_base_yaw(view_name)
            pitch0 = 0.0
            dist0 = _dist(cam_pos, pivot) or CAMERA_DEFAULT_DIST
        else:
            vec = [cam_pos[0] - pivot[0], cam_pos[1] - pivot[1], cam_pos[2] - pivot[2]]
            yaw0, pitch0, dist0 = _compute_yaw_pitch_dist_from_vector(vec, up_axis)
        _angle_view_baseline[view_name] = {
            "yaw": yaw0,
            "pitch": pitch0,
            "dist": dist0,
            "pivot": [float(pivot[0]), float(pivot[1]), float(pivot[2])],
        }
    except Exception as e:
        _logger.warning(f"Failed to ensure baseline for {view_name}: {e}")


def _get_angle_base_yaw(view_name):
    if view_name == "正":
        return 0.0
    if view_name == "30°":
        return 30.0
    if view_name == "40°":
        return 40.0
    if view_name == "右":
        return 90.0
    return 0.0


def _get_camera_shape(camera_transform):
    shapes = cmds.listRelatives(camera_transform, shapes=True, type="camera") or []
    return shapes[0] if shapes else None


def _get_pivot_point_ws():
    """Get world-space pivot (center) around head for camera orbit.

    Prefers const.BAK_HEAD_MESH_NAME, then const.BASE_HEAD_MESH_NAME; if none, use all visible meshes.
    """
    try:
        candidates = []
        # Prefer explicit names
        for name in (const.BAK_HEAD_MESH_NAME, const.BASE_HEAD_MESH_NAME):
            if name and cmds.objExists(name):
                # If it's a shape, get its parent transform
                if cmds.nodeType(name) == "mesh":
                    parents = cmds.listRelatives(name, parent=True, fullPath=True) or []
                    if parents:
                        candidates.append(parents[0])
                else:
                    candidates.append(name)
                break

        # Fallback: all visible meshes' transforms
        if not candidates:
            shapes = cmds.ls(type="mesh", visible=True) or []
            if shapes:
                transforms = cmds.listRelatives(shapes, parent=True, fullPath=True) or []
                candidates = list(set(transforms))

        if candidates:
            bbox = cmds.exactWorldBoundingBox(candidates)
            # bbox = [xmin, ymin, zmin, xmax, ymax, zmax]
            cx = 0.5 * (bbox[0] + bbox[3])
            cy = 0.5 * (bbox[1] + bbox[4])
            cz = 0.5 * (bbox[2] + bbox[5])
            return [float(cx), float(cy), float(cz)]
    except Exception as e:
        _logger.warning(f"Failed to get pivot center: {e}")
    return [0.0, 0.0, 0.0]


def _dist(a, b):
    dx = a[0] - b[0]
    dy = a[1] - b[1]
    dz = a[2] - b[2]

    return math.sqrt(dx * dx + dy * dy + dz * dz)


def _aim_at(camera_transform, target_pos):
    try:
        loc = cmds.spaceLocator()[0]
        cmds.xform(loc, ws=True, t=target_pos)
        con = cmds.aimConstraint(
            loc,
            camera_transform,
            aimVector=[0, 0, -1],  # camera looks down -Z
            worldUpType="scene",  # use Maya scene up axis (Y-up or Z-up)
            mo=False,
        )
        cmds.delete(con)
        cmds.delete(loc)
    except Exception as e:
        _logger.warning(f"Aim failed for {camera_transform}: {e}")


def _apply_angle_camera_transform(view_name):
    try:
        if view_name not in ORTHOGRAPHIC_VIEW_SET:
            return False
        # Ensure camera exists for angle views
        if view_name == "30°":
            ensure_angle_camera_exists("angle30", 30.0)
        elif view_name == "40°":
            ensure_angle_camera_exists("angle40", 40.0)

        cam = _get_camera_name_from_view(view_name)
        if not cam or not cmds.objExists(cam):
            return False

        # Keep orthographic
        cam_shape = _get_camera_shape(cam)
        if cam_shape:
            try:
                cmds.setAttr(f"{cam_shape}.orthographic", 1)
            except Exception:
                pass

        # Get pivot and baseline (yaw/pitch/dist) and reuse baseline pivot for stability
        current_pivot = _get_pivot_point_ws()
        cam_pos = cmds.xform(cam, q=True, ws=True, t=True)

        _ensure_angle_baseline(view_name, current_pivot)
        base = _angle_view_baseline.get(view_name) or {
            "yaw": _get_angle_base_yaw(view_name),
            "pitch": 0.0,
            "dist": _dist(cam_pos, current_pivot) or CAMERA_DEFAULT_DIST,
            "pivot": [float(current_pivot[0]), float(current_pivot[1]), float(current_pivot[2])],
        }
        pivot = base.get("pivot", current_pivot)
        dist = base["dist"]

        # Apply offsets on top of baseline
        h = float(_angle_view_offsets[view_name]["h"])  # -15..15
        v = float(_angle_view_offsets[view_name]["v"])  # -15..15
        yaw = base["yaw"] + h
        pitch = base["pitch"] + v

        # reuse trig
        up_axis = cmds.upAxis(query=True, axis=True)
        cos = math.cos
        sin = math.sin
        yaw_rad = math.radians(yaw)
        pitch_rad = math.radians(pitch)
        cpy = cos(pitch_rad)
        spy = sin(pitch_rad)
        sy = sin(yaw_rad)
        cy = cos(yaw_rad)

        if up_axis == "y":
            hr = dist * cpy
            x = pivot[0] + hr * sy
            y = pivot[1] + dist * spy
            z = pivot[2] + hr * cy
        else:
            hr = dist * cpy
            x = pivot[0] + hr * sy
            y = pivot[1] - hr * cy  # Z-up: Y toward -cos at yaw=0 -> negative Y at 30°
            z = pivot[2] + dist * spy

        # Place and set explicit Euler to keep canonical front-facing orientation
        # Write placement and aim to keep pivot centered; aim uses scene up to avoid flips
        cmds.xform(cam, ws=True, t=[x, y, z])
        _aim_at(cam, pivot)
        return True
    except Exception as e:
        _logger.error(f"Failed to apply angle camera transform for {view_name}: {e}")
        return False


def set_angle_view_horizontal_rotation(view_name, degrees):
    """Set horizontal rotation offset for ortho views (正/30°/40°/右) (-15~15 degrees)."""
    try:
        if view_name not in ORTHOGRAPHIC_VIEW_SET:
            return False
        val = max(-ROTATE_OFFSET_LIMIT_DEG, min(ROTATE_OFFSET_LIMIT_DEG, float(degrees)))
        _angle_view_offsets[view_name]["h"] = val
        ok = _apply_angle_camera_transform(view_name)
        return ok
    except Exception as e:
        _logger.error(f"Failed to set horizontal rotation for {view_name}: {e}")
        return False


def set_angle_view_vertical_rotation(view_name, degrees):
    """Set vertical rotation offset for ortho views (正/30°/40°/右) (-15~15 degrees)."""
    try:
        if view_name not in ORTHOGRAPHIC_VIEW_SET:
            return False
        val = max(-ROTATE_OFFSET_LIMIT_DEG, min(ROTATE_OFFSET_LIMIT_DEG, float(degrees)))
        _angle_view_offsets[view_name]["v"] = val
        ok = _apply_angle_camera_transform(view_name)
        return ok
    except Exception as e:
        _logger.error(f"Failed to set vertical rotation for {view_name}: {e}")
        return False


def reset_angle_view(view_name):
    """Reset 30°/40° view to canonical yaw=30/40, pitch=0, around head pivot, dist=1000."""
    try:
        if view_name not in ORTHOGRAPHIC_VIEW_SET:
            return False

        base_yaw = _get_angle_base_yaw(view_name)
        cam = _get_camera_name_from_view(view_name)
        if not cam:
            cam = "angle30" if view_name == "30°" else ("angle40" if view_name == "40°" else None)
        if view_name in ("30°", "40°") and cam:
            ensure_angle_camera_exists(cam, base_yaw)

        # Compute and SET BOTH translate and rotate to canonical placement around head pivot
        up_axis = cmds.upAxis(query=True, axis=True)

        dist = CAMERA_DEFAULT_DIST
        yaw_rad = math.radians(base_yaw)
        pivot = _get_pivot_point_ws()
        if up_axis == "y":
            # Y-up: place on XZ circle and face pivot
            x = pivot[0] + dist * math.sin(yaw_rad)
            y = pivot[1]
            z = pivot[2] + dist * math.cos(yaw_rad)
            cmds.xform(cam, ws=True, t=[x, y, z])
            cmds.setAttr(f"{cam}.rotateX", 0)
            cmds.setAttr(f"{cam}.rotateY", base_yaw)
            cmds.setAttr(f"{cam}.rotateZ", 0)
        else:
            # Z-up: place on XY circle and face pivot
            x = pivot[0] + dist * math.sin(yaw_rad)
            y = pivot[1] + dist * math.cos(yaw_rad)
            z = pivot[2]
            cmds.xform(cam, ws=True, t=[x, y, z])
            cmds.setAttr(f"{cam}.rotateX", 90)
            cmds.setAttr(f"{cam}.rotateY", 0)
            cmds.setAttr(f"{cam}.rotateZ", base_yaw)

        # Keep orthographic and a sensible width
        cam_shape = _get_camera_shape(cam)
        if cam_shape:
            try:
                cmds.setAttr(f"{cam_shape}.orthographic", 1)
                if cmds.objExists(f"{cam_shape}.orthographicWidth"):
                    cmds.setAttr(f"{cam_shape}.orthographicWidth", ORTHO_WIDTH_DEFAULT)
            except Exception:
                pass

        # Reset offsets and baseline to canonical using dist & pivot above
        _angle_view_offsets[view_name] = {"h": 0.0, "v": 0.0}
        _angle_view_baseline[view_name] = {
            "yaw": base_yaw,
            "pitch": 0.0,
            "dist": dist,
            "pivot": [float(pivot[0]), float(pivot[1]), float(pivot[2])],
        }

        # Ensure the active panel looks through the camera and refresh
        panel = _get_active_model_panel()
        if panel and cam:
            try:
                cmds.modelEditor(panel, edit=True, camera=cam)
            except Exception:
                pass
        try:
            cmds.refresh()
        except Exception:
            pass
        return True
    except Exception as e:
        _logger.error(f"Failed to reset angle view for {view_name}: {e}")
        return False
