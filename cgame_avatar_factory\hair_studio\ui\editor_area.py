"""Editor Area Module.

This module provides the editor area widget for the Hair Studio tool.
It allows users to view and edit the properties of the selected hair component.
"""

# Import built-in modules
# Import standard library
import logging

# Import third-party modules
# Import dayu widgets
from dayu_widgets import M<PERSON>abel

# Import Qt modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.constants import FOCUS_POLICY_NO_FOCUS
from cgame_avatar_factory.hair_studio.constants import FOCUS_OUTLINE_DISABLED_STYLE
from cgame_avatar_factory.hair_studio.constants import FORM_INPUT_STYLE
from cgame_avatar_factory.hair_studio.constants import FORM_LABEL_STYLE
from cgame_avatar_factory.hair_studio.constants import FORM_SEPARATOR_STYLE
from cgame_avatar_factory.hair_studio.constants import LAYOUT_SPACING_MEDIUM
# Clearing iteration constants removed - no longer needed for simplified editor
from cgame_avatar_factory.hair_studio.constants import NO_COMPONENT_STYLE
from cgame_avatar_factory.hair_studio.constants import SUBTITLE_STYLE
from cgame_avatar_factory.hair_studio.constants import UI_TEXT_NO_COMPONENT_SELECTED
from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager


class EditorArea(QtWidgets.QScrollArea):
    """Editor Area Widget.

    This widget displays and allows editing of the properties of the selected hair component.
    It shows different properties based on the type of hair component selected.
    """

    def __init__(self, hair_type, hair_manager=None, parent=None):
        """Initialize the EditorArea.

        Args:
            hair_type (str): Type of hair ('card', 'xgen', 'curve')
            hair_manager (HairManager, optional): The hair manager instance. If None, a new one will be created.
            parent (QWidget, optional): The parent widget. Defaults to None.
        """
        super(EditorArea, self).__init__(parent)
        self.hair_type = hair_type
        self.object_name = "{}EditorArea".format(hair_type.capitalize())
        self.setObjectName(self.object_name)

        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        # Initialize manager
        self.manager = hair_manager if hair_manager is not None else HairManager(parent)

        # Current component data
        self.current_component = None

        # Initialize UI
        self.setup_ui()

    def setup_ui(self):
        """Set up the user interface components."""
        # Create container widget
        container = QtWidgets.QWidget()
        self.setWidget(container)
        self.setWidgetResizable(True)

        # Remove focus outline from scroll area to prevent blue border
        self.setFocusPolicy(QtCore.Qt.FocusPolicy(FOCUS_POLICY_NO_FOCUS))
        container.setFocusPolicy(QtCore.Qt.FocusPolicy(FOCUS_POLICY_NO_FOCUS))

        # Apply form input styles and focus outline removal to the container
        container.setStyleSheet(
            FORM_INPUT_STYLE + FORM_LABEL_STYLE + FORM_SEPARATOR_STYLE + FOCUS_OUTLINE_DISABLED_STYLE,
        )

        # Main layout with consistent spacing
        main_layout = QtWidgets.QVBoxLayout(container)
        main_layout.setContentsMargins(
            LAYOUT_SPACING_MEDIUM,
            LAYOUT_SPACING_MEDIUM,
            LAYOUT_SPACING_MEDIUM,
            LAYOUT_SPACING_MEDIUM,
        )
        main_layout.setSpacing(LAYOUT_SPACING_MEDIUM)

        # Title with consistent styling
        self.title_label = MLabel("毛发编辑区")
        self.title_label.setProperty("h2", True)
        self.title_label.setStyleSheet(SUBTITLE_STYLE)
        self.title_label.setAlignment(QtCore.Qt.AlignCenter)
        main_layout.addWidget(self.title_label)

        # Add separator with consistent styling
        separator = self._create_separator()
        main_layout.addWidget(separator)

        # Simple content area - no form layout needed since we only show component name
        self.content_label = MLabel("请选择一个毛发组件")
        self.content_label.setAlignment(QtCore.Qt.AlignCenter)
        main_layout.addWidget(self.content_label)

        # Add stretch to push content to the top
        main_layout.addStretch()

        # Set initial state
        self.set_component(None)

    def _create_separator(self):
        """Create a styled separator widget."""
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        return separator

    # Form input creation method removed - no longer needed for simplified editor

    def set_component(self, component_data):
        """Set the current component to edit.

        Args:
            component_data (dict): Dictionary containing component data
        """
        self.current_component = component_data

        if not component_data:
            self.title_label.setText(UI_TEXT_NO_COMPONENT_SELECTED)
            # Apply no component selected style for consistent height
            self.title_label.setStyleSheet(NO_COMPONENT_STYLE)
            self.content_label.setText("请选择一个毛发组件")
            return

        # Update title - only show the component name, no detailed properties
        component_name = component_data.get("name", "Unnamed Component")
        self.title_label.setText(component_name)
        # Apply subtitle style for component name
        self.title_label.setStyleSheet(SUBTITLE_STYLE)

        # Update content label to show component info
        self.content_label.setText(f"已选择组件: {component_name}")

        # Only show component name, no detailed properties anymore
        # The detailed editing functionality has been moved to separate areas

    def clear_component(self):
        """Clear the current component and reset the editor area."""
        self.current_component = None

        # Update title
        self.title_label.setText(UI_TEXT_NO_COMPONENT_SELECTED)
        # Apply no component selected style for consistent height
        self.title_label.setStyleSheet(NO_COMPONENT_STYLE)

        # Reset content label
        self.content_label.setText("请选择一个毛发组件")

    # Form clearing methods removed - no longer needed for simplified editor

    # Property editing methods removed - EditorArea now only shows component name
    # Detailed property editing has been moved to separate areas
