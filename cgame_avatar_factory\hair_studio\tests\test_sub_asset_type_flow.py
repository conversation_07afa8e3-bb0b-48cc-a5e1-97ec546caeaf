"""Test sub_asset_type data flow from data manager to Maya API."""

# Import built-in modules
import unittest

# Import local modules
# Import the modules we need to test
from cgame_avatar_factory.hair_studio.data.models import HairAsset


class TestSubAssetTypeFlow(unittest.TestCase):
    """Test that sub_asset_type flows correctly through the system."""

    def test_hair_asset_sub_asset_type_support(self):
        """Test that HairAs<PERSON> properly handles sub_asset_type."""
        # Test creating HairAsset with sub_asset_type
        asset = HairAsset(
            id="test_001",
            name="Test Hair",
            asset_type="card",
            sub_asset_type="scalp",
            thumbnail="/path/to/thumb.jpg",
            metadata={"file_path": "/path/to/asset.fbx", "reference": "/path/to/ref.obj"},
        )

        # Verify the attribute is set
        self.assertEqual(asset.sub_asset_type, "scalp")

        # Test to_dict includes sub_asset_type
        asset_dict = asset.to_dict()
        self.assertIn("sub_asset_type", asset_dict)
        self.assertEqual(asset_dict["sub_asset_type"], "scalp")

        # Test from_dict preserves sub_asset_type
        recreated_asset = HairAsset.from_dict(asset_dict)
        self.assertEqual(recreated_asset.sub_asset_type, "scalp")

    def test_hair_asset_none_sub_asset_type(self):
        """Test that HairAsset handles None sub_asset_type correctly."""
        asset = HairAsset(
            id="test_002",
            name="Test Hair",
            asset_type="card",
            sub_asset_type=None,
        )

        self.assertIsNone(asset.sub_asset_type)

        asset_dict = asset.to_dict()
        self.assertIn("sub_asset_type", asset_dict)
        self.assertIsNone(asset_dict["sub_asset_type"])


if __name__ == "__main__":
    unittest.main()
